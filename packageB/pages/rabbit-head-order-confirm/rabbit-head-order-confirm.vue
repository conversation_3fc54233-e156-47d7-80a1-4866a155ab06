<template>
	<view class="content h-vh-100 bg-f5f5f5 o-scr-y">
		<!-- 导航栏 -->
		<vh-navbar title="确认订单" />
		
		<!-- 确认订单信息加载完成 -->
		<view v-if="!loading" class="fade-in pt-20 pb-20">
			<!-- 收货地址（有数据） -->
			<view v-if="Object.keys(addressInfo).length" class="bg-ffffff b-rad-10 d-flex j-sb a-center ml-24 mr-24 ptb-32-plr-24" @click="jump.navigateTo(`${routeTable.pEAddressManagement}?comeFrom=3`)">
				<view class="w-500">
					<view class="d-flex a-center">
						<text class="font-32 font-wei text-3">{{addressInfo.consignee}}</text>
						<text class="ml-14 font-28 text-9">{{addressInfo.consignee_phone}}</text>
					</view>
					<view class="mt-12">
						<text v-if="addressInfo.is_default" class="bg-ff0013 b-rad-04 ptb-02-plr-08 text-ffffff font-20 font-wei l-h-28">默认</text>
						<text v-if="addressInfo.label" class="bg-2e7bff b-rad-04 ptb-02-plr-16 text-ffffff font-20 font-wei l-h-28" :class="addressInfo.is_default ? 'ml-10' : ''">{{addressInfo.label}}</text>
						<text class="font-24 text-3 l-h-34" :class="addressInfo.is_default || addressInfo.label ? 'ml-10' : ''">{{addressInfo.province_name}} {{addressInfo.city_name}} {{addressInfo.town_name}} {{addressInfo.address}}</text>
					</view>
				</view>
					
				<u-icon name="arrow-right" :size="24" color="#333" />
			</view>
			
			<!-- 收货地址（无数据） -->
			<view v-else class="p-rela h-188 b-rad-10 d-flex j-center a-center ml-24 mr-24 o-hid" @click="jump.navigateTo(`${routeTable.pEAddressManagement}?comeFrom=3`)">
				<image class="p-abso z-01 w-702 h-188" src="http://images.vinehoo.com/vinehoomini/v3/order-confirm/add_bg.png" mode="aspectFill"></image>
				<view class="p-rela z-02 d-flex flex-column j-center a-center">
					<image class="w-84 h-84" src="http://images.vinehoo.com/vinehoomini/v3/order-confirm/add_ico.png" mode="aspectFill"></image>
					<text class="mt-06 font-30 text-3">新建收货地址</text>
				</view>
			</view>
		    
			<!-- 兔头商品 -->
			<view class="bg-ffffff b-rad-10 mt-20 ml-24 mr-24 pt-32 pr-24 pb-32 pl-24" v-for="(item, index) in rabbitOrderInfo.rabbitGoodsList" :key="index">
				<view class="font-32 font-wei text-3 l-h-44">兑换商品</view>
				
				<view class="d-flex mt-28">
					<view class="w-246 h-152 b-rad-06 o-hid">
						<vh-image :loading-type="2" :src="item.banner_img" :height="152" />
					</view>
					
					<view class="flex-1 d-flex flex-column j-sb ml-12">
						<view class="">
							<view class="font-24 text-0 l-h-34 text-hidden-2">{{item.title}}</view>
							<text class="bg-f5f5f5 b-rad-04 mt-08 ptb-02-plr-12 font-20 text-9">{{item.package_name}}</text>
						</view>
						<view class="d-flex j-sb a-center">
							<view class="">
								<text class="font-28 font-wei text-e80404 l-h-40">{{item.rabbit}}</text>
								<image class="ml-06 w-28 h-28" src="http://images.vinehoo.com/vinehoomini/v3/comm/rab_gold.png" mode="aspectFill" />
							</view>
							<view class="font-24 text-6">x{{item.nums}}</view>
						</view>
					</view>
				</view>
			
			    <view class="mt-32 d-flex j-sb a-center">
			    	<text class="font-28 font-wei text-3 l-h-40">配送</text>
					<text class="font-24 text-3 l-h-36">默认配送方式</text>
			    </view>
				
				<view class="mt-20 font-24 text-6">预计发货时间：{{item.predict_time}}</view>
			</view>
		
			<!-- 温馨提示 -->
			<view class="bg-ffffff b-rad-10 mt-20 ml-24 mr-24">
				<view class="ml-24 mr-24 pt-32 pb-32 font-28 text-9">温馨提示：不支持7天无理由退货</view>
			</view>
			
			<!-- 联系客服 -->
			<view class="p-abso bottom-160 w-p100">
				<view class="d-flex j-center">
					<text class="font-24 text-9">如有特殊需求，请</text>
					<text class="font-24 text-2e7bff" @click="system.customerService($vhFrom)">联系客服</text>
					<text class="font-24 text-9">。</text>
				</view>
			</view>
		
		    <!-- 结算按钮 -->
			<view class="p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff b-sh-00021200-022 d-flex j-sb a-center pl-32 pr-24">
				<view class="d-flex a-center">
					<text class="font-28 font-wei text-3">合计：</text>
					<text class="font-40 font-wei text-e80404">{{rabbitOrderInfo.rabbitGoodsList[0].rabbit}}</text>
					<image class="ml-06 w-28 h-28" src="http://images.vinehoo.com/vinehoomini/v3/comm/rab_gold.png" mode="aspectFill" />
				</view>
				
				<view class="">
					<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF" 
					:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#FFF', backgroundColor: '#E80404', border:'none'}"
					@click="exchangeNow">立即兑换</u-button>
				</view>
			</view>
		    
			<!-- 弹框 -->
			<view class="">
				<!-- 离开弹框 -->
				<u-modal
					v-model="showRabNotEnoMod" 
					:width="540"
					:show-title="false"
					:show-cancel-button="true"
					cancel-text="知道了"
					confirm-text="去赚兔头"
					:cancel-style="{fontSize:'28rpx', color:'#999'}"
					:confirm-style="{fontSize:'28rpx', color:'#E80404'}"
					:content-style="{fontSize:'28rpx', fontWeight:'bold', color:'#333'}"
					@confirm="jump.redirectTo( routeTable.pEDailyTasks )">
					<view class="ptb-52-plr-00 text-center">您的兔头数量不足，无法兑换</view>
					</u-modal>
			</view>
		</view>
		
		<!-- 骨架屏 -->
		<vh-skeleton v-else :type="2" loading-color="#FF9127" />
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	
	export default{
		name: 'rabbit-head-order-confirm',
		
		data() {
			return {
				loading: true, //加载状态 true = 加载中、false = 结束加载
				rabbitNum:0, //兔头数量
				addressInfo: {}, //地址信息
				showRabNotEnoMod: false, //显示兔头不够的弹框
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['routeTable', 'rabbitOrderInfo', 'addressInfoState']),
		},
		
		onShow() {
			this.init()
		},
		
		methods: {
			
			// 初始化
			async init() {
				await Promise.all([this.getRabbitNum(),this.getAddressList()])
				this.loading = false
			},
			
			// 获取兔头数
			async getRabbitNum() {
				let res = await this.$u.api.userSpecifiedData({field: 'rabbit'})
				this.rabbitNum = res.data.rabbit
			},
			
			// 获取收货地址列表
			async getAddressList() {
				if( Object.keys(this.addressInfoState).length ) { //如果用户从新选择了地址信息，就读选择的地址信息（从vuex取）
					this.addressInfo = this.addressInfoState
				}else{ //否则就从地址收货地址列表里面取数据，规则：有默认地址就取默认地址、没有默认地址就取数组第一项，否则就代表没有收货地址
					let res = await this.$u.api.addressList()
					if(res.data.list.length > 0) {
						for(let i = 0; i < res.data.list.length; i++) {
							if(res.data.list[i].is_default) {
								this.addressInfo = res.data.list[i]
								break
							}else{
								this.addressInfo = res.data.list[0]
							}
						}
					}else{
						this.addressInfo = {}
					}
				}
				// let res = await this.$u.api.addressList()
				// if(res.data.list.length > 0) {
				// 	for(let i = 0; i < res.data.list.length; i++) {
				// 		res.data.list[i].is_default ? this.addressInfo = res.data.list[i] : this.addressInfo = res.data.list[0]
				// 	}
				// }
				// this.loading = false
			},
			
			// 立即兑换
			async exchangeNow() {
				// console.log(this.rabbitOrderInfo.rabbitGoodsList[0].rabbit)
				if( this.rabbitNum < this.rabbitOrderInfo.rabbitGoodsList[0].rabbit ) return this.showRabNotEnoMod = true //用户兔头数量小于商品需要兑换的兔头数量
				const { rabbitGoodsList } = this.rabbitOrderInfo //兔头订单信息
				const { province_id, city_id, town_id, address, consignee, consignee_phone} = this.addressInfo //地址信息
				let data = {
					items_info: rabbitGoodsList, //兔头商品信息
					order_from: 4, //来源: 0-IOS 1-Android 2-H5 3-PC 4-Wxapp
					province_id, //省id
					city_id, //市id
					district_id: town_id, //区id
					address, //详细地址
					consignee, //收货人姓名
					consignee_phone, //收货人电话号码
				}
				try{
					this.feedback.loading({ title:'提交中...' })
					let res = await this.$u.api.rabbitCreateOrder(data)
					this.feedback.hideLoading()
					this.jump.redirectTo( this.routeTable.pBRabbitExchangeSuccess )
				}catch(e){
					setTimeout(() => { this.jump.navigateBack() }, 1500)
				}
			},
		}
	}
</script>

<style scoped></style>

