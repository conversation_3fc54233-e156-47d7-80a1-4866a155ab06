<template>
	<view class="content">
		<!-- 导航栏 -->
		<view class="">
			<vh-navbar v-if="from == ''" title="商品详情" />
			
			<view v-else class="p-fixed z-980 top-0 w-p100 bg-ffffff">
				<view :style="{ height: appStatusBarHeight + 'px'}" />
				<view class="p-rela h-px-48 d-flex j-center a-center">
					<view class="p-abso left-24 h-p100 d-flex a-center" @click="appJumpBack()">
						<u-icon name="nav-back" color="#333" :size="44" />
					</view>
					<view class="font-36 font-wei text-3">商品详情</view>
					<!-- <view class="w-56">  -->
						<image  v-show="from == '1' || from == '2'" class="p-abso right-0 w-56 h-56 p-14" :src="ossIcon('/goods_detail/nav_sha_bla.png')" @click="onShare" /> 
					<!-- </view> -->
					
				</view>
			</view>
		</view>
		
		<!-- bann -->
		<image :style="{paddingTop: from == '' ? '0px' : parseInt(appStatusBarHeight) + 48 + 'px'}" class="w-p100 h-462" :src="rabbitGoodsInfo.banner_img && rabbitGoodsInfo.banner_img[0]" mode="aspectFill" />
		
		<!-- 商品名称、价格 -->
		<view class="pt-26 pr-24 pb-32 pl-24">
			<view class="d-flex j-sb a-center">
				<view class="">
					<text class="font-52 font-wei text-e80404">{{rabbitGoodsInfo.rabbit}}</text>
					<image class="ml-04 w-26 h-28" src="https://images.vinehoo.com/vinehoomini/v3/mine/rab_coi_gold.png" mode="widthFix" />
				</view>
				<view class="font-28 text-6">已兑<text class="font-40 text-e80404">{{limitInfo.aleradyBuy}}</text>份</view>
			</view>
			<view class="mt-38 font-30 font-wei text-3">{{rabbitGoodsInfo.title}}</view>
			<view class="mt-12 font-28 text-6">{{rabbitGoodsInfo.brief}}</view>
		</view>
		
		<!-- 分割线 -->
		<vh-gap height="20" bg-color="#F5F5F5" />
		
		<!-- 预计发货时间 -->
		<view class="d-flex a-center b-rad-10 pt-40 pb-34 pl-24">
			<text class="font-28 font-wei text-3">预计发货时间</text>
			<text class="ml-24 font-28 text-3">{{estimatedDeliveryTime}}</text>
			<text class="font-28 text-6">（不支持7天无理由退货）</text>
		</view>
		
		<!-- 分割线 -->
		<vh-gap height="20" bg-color="#F5F5F5" />
		
		<!-- 商品详情 -->
		<view class="pt-32 pl-24 pb-104 pr-24">
			<view class="text-center font-32 font-wei text-3">商品详情</view>
			<view class="mt-32 w-b-b-w">
				<u-parse :html="rabbitGoodsInfo.detail" :show-with-animation="true" />
			</view>
		</view>
	
	    <!-- 底部按钮 -->
		<view class="">
			<view v-if="from == '3'" class="p-fixed z-999 bottom-0 w-p100 h-104 bg-ffffff d-flex j-center a-center b-sh-00021200-022 font-32 font-wei text-e80404">文案预览</view>
			<view v-else class="bg-ffffff p-fixed z-999 bottom-0 w-p100 h-104 d-flex j-center a-center b-sh-00021200-022" v-safeBeautyBottom="$safeBeautyBottom">
				<view v-if="packageAllInfo.onsale_status != 2" class="">
					<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
					:custom-style="{width:'646rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: '#DDDDDD', border:'none'}"
					@click="feedback.toast({title:`${ packageAllInfo.onsale_status < 2 ? '亲，该商品仅限兑换~' : '亲，该商品已经兑换结束啦~'}`})">
					{{packageAllInfo.onsale_status < 2 ? '仅限兑换' : '兑换结束'}}</u-button>
				</view>
				<view v-else class="">
					<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
					:custom-style="{width:'646rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#FFF', backgroundColor: '#E80404', border:'none'}" @click="exchangeNow">立即兑换</u-button>
				</view>
			</view>
		</view>
		
		<!-- 弹框 -->
		<view class="">
			<!-- 套餐弹框 -->
			<u-popup v-model="showCollPop" mode="bottom" :border-radius="20">
				<view class="pt-32 pr-24 pb-20 pl-24">
					<view class="d-flex">
						<view class="w-246 h-152 b-rad-06 o-hid">
							<vh-image :loading-type="2" :src="rabbitGoodsInfo.banner_img && rabbitGoodsInfo.banner_img[0]" :height="152" />
						</view>
						<view class="flex-1 d-flex flex-column j-sb ml-16">
							<view class="font-28 text-3 o-hid text-hidden-2">{{rabbitGoodsInfo.title}}</view>
							<view class="d-flex a-center">
								<text class="font-44 font-wei text-e80404">{{packageInfo.rabbit}}</text>
								<image class="w-26 h-28 ml-04" src="https://images.vinehoo.com/vinehoomini/v3/mine/rab_coi_gold.png" mode="widthFix" />
							</view>
						</view>
					</view>
					
					<view class="mt-48 font-32 font-wei text-3">规格</view>
					
					<view class="d-flex flex-wrap ml-n-24">
						<view class="bg-f6f6f6 mt-32 ml-24 ptb-04-plr-32 b-rad-24 font-28 text-3" :class="packageIndex == index ? 'bg-fce4e3 b-s-01-e80404 text-e80404' : ''" 
						v-for="(item, index) in packageList" :key="index" @click="selectPackage(index)">{{item.package_name}}</view>
					</view>
					<view class="mt-92 d-flex j-center a-center">
						<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
						:custom-style="{width:'646rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#FFF', backgroundColor: '#E80404', border:'none'}"
						@click="confirmExchangeNow">立即兑换</u-button>
					</view>
				</view>
			</u-popup>
		</view>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	export default{
		name: 'rabbit-head-goods-detail',
		
		data() {
			return {
				loading: true, //加载状态
				from:'', //从哪个端进入 1 = 安卓、2 = ios"、3 = pc
				appStatusBarHeight:'', //状态栏高度
				goodsId:'', //商品id
				rabbitGoodsInfo: {}, //兔头商品信息
				packageAllInfo:{}, //套餐所有信息
				limitInfo:{}, //限购信息
				estimatedDeliveryTime:'', //预计发货时间
				packageList: [], //酒会套餐列表
				packageIndex: 0, //选中的套餐索引
				packageInfo:{}, //套餐信息
				showCollPop: false,//是否展示套餐信息
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['rabbitOrderInfo', 'routeTable']),
		},
		
		onLoad(options) {
			this.goodsId = parseInt(options.id) //商品id（商品期数）
			// if(options.from){
			// 	this.from = options.from 
			// 	this.muFrom(this.from) //保存来自哪个状态（解决用户在安卓、ios端登录失效的问题）
			// }
			if(options.from && options.statusBarHeight){
				this.from = options.from
				this.appStatusBarHeight = options.statusBarHeight //app状态栏高度
				this.muFrom(this.from) //保存来自哪个状态（解决用户在安卓、ios端登录失效的问题）
			}
			this.init()
		},
		
		methods: {
			// Vuex mapMutations辅助函数
			...mapMutations(['muRabbitOrderInfo', 'muFrom']),
			
			// 初始化（接口聚合:  兔头商品详情、兔头商品套餐详情）
			async init() {
				try {
					await this.getRabbitGoodsDetail()
					await this.getRabbitPackageDetail()
					this.loading = false
					uni.stopPullDownRefresh() //停止下拉刷新
				}
				catch(e) { 
					console.log('----我报异常了')
					console.log(e)
					this.goBack() 
				}
			},
			
			// 获取兔头商品详情
			async getRabbitGoodsDetail() {
				const t = this.from === '3' ? Date.now() : 1
				let res = await this.$u.api.goodsDetailJson({ t, isJson:true, id: this.goodsId })
				this.rabbitGoodsInfo = res.data // 兔头商品详情
				// this.estimatedDeliveryTime = this.calEstimateTime() //前端计算预计发货时间
			},
			
			// 获取兔头套餐详情
			async getRabbitPackageDetail(){
			  // if(this.from !== '3') {
				 //  let data = {}
				 //  data.id = this.goodsId
				 //  let res = await this.$u.api.packageDetail(data)
				 //  this.packageList = res.data.packageList.filter(v => {return v.is_hidden == 0}) //兔头商品套餐列表
				 //  this.packageInfo = res.data.packageList[this.packageIndex] //选中的兔头商品套餐
				 //  this.limitInfo = { aleradyBuy: res.data.purchased, limitNumber: res.data.limit_number }
			  // }
			  
			  let data = {}
			  data.period = this.goodsId //商品期数
			  data.periods_type = this.rabbitGoodsInfo.periods_type //商品频道
			  let res = await this.$u.api.packageDetail(data)
			  let {purchased, limit_number, packageList} = res.data
			  this.packageAllInfo = res.data //所有套餐信息
			  this.estimatedDeliveryTime = this.calEstimateTime() //前端计算预计发货时间
			  this.packageList = res.data.packageList.filter(v => {return v.is_hidden == 0}) //兔头商品套餐列表
			  this.limitInfo = { aleradyBuy: purchased, limitNumber: limit_number }
			  this.packageInfo = packageList[this.packageIndex]
			},
			
			// 发生错误返回首页
			goBack() {
				setTimeout(() => {
					if(this.comes.isFromApp(this.from)){ //判断是否从App过来 1 = 安卓 2 = ios
						wineYunJsBridge.openAppPage({
							client_path: { "ios_path":"goBack", "android_path":"goBack" }
						})
					}else{//h5端
						this.jump.navigateBack()
					}
				}, 1500)
			},
			
			// app返回
			appJumpBack() {
				wineYunJsBridge.openAppPage({ client_path: { "ios_path":"goBack", "android_path":"goBack" } });
			},
			
			// 计算预计发货时间
			calEstimateTime() {
				const { predict_shipment_time } = this.packageAllInfo //所有套餐信息
				let curDate = new Date() //当前时间
				let shipDate  = new Date(predict_shipment_time.replace(/-/g, '/')) //结束时间
				let seconds = Math.floor((shipDate.getTime() - curDate.getTime()) / 1000 ) //得到两个日期之间的秒数
				if(seconds >= 0){
					console.log('---------预计发货时间大于等于当前时间')
					return predict_shipment_time.substr(0, 10)
				} else {
					console.log('---------预计发货时间小于当前时间')
					let ruleShipmentDate = this.$u.timeFormat(new Date(curDate.getTime() + 24 * 60 * 60 * 1000).getTime(), 'yyyy-mm-dd')
					console.log(ruleShipmentDate)
					return ruleShipmentDate
				}
			},
			
			// 选择套餐 index = 酒会列表索引
			selectPackage(index) {
				console.log(index)
				this.packageIndex = index
				this.packageInfo = this.packageList[this.packageIndex] //选中的兔头商品套餐
			},
			
			// 立即兑换
			exchangeNow() {
				if(this.login.isLogin(this.from)){
					this.showCollPop = true
				}
			},
			
			// 确认立即兑换
			confirmExchangeNow() {
				console.log('-----------------确认立即兑换')
				const {banner_img, title } = this.rabbitGoodsInfo //兔头商品信息
				const { rabbit, package_name} = this.packageInfo //兔头商品套餐信息
				let rabbitOrderInfo = { 
					rabbitGoodsList: [], //兔头商品列表
				}
				let item = {} //兔头商品信息
				item.period = this.rabbitGoodsInfo.id //期数id
				item.banner_img = banner_img[0] //兔头商品banner图
				item.title = title //兔头商品标题
				item.package_id = this.packageInfo.id //套餐id
				item.nums = 1 //套餐购买数量
				item.package_name = package_name //套餐名称
				item.rabbit = rabbit //兔头数量（兔头兑换数量）
				item.predict_time = this.estimatedDeliveryTime //预计发货时间
				rabbitOrderInfo.rabbitGoodsList.push(item) //兔头订单信息
				console.log('--------------------------这是确认立即兑换需要的数据')
				console.log(rabbitOrderInfo)
	
				this.muRabbitOrderInfo(rabbitOrderInfo)
				
				// 分条件跳转
				if(this.comes.isFromApp(this.from)){ //判断是否从App过来 1 = 安卓 2 = ios
					wineYunJsBridge.openAppPage({
						client_path: {"ios_path":"rabbitConfirmOrder", "android_path":"rabbitConfirmOrder" }, 
						ad_path_param: [{"ios_key":"info", "ios_val": JSON.stringify(rabbitOrderInfo), "android_key":"info", "android_val": JSON.stringify(rabbitOrderInfo) }]
					});
				}else{//h5端
					this.jump.navigateTo('../rabbit-head-order-confirm/rabbit-head-order-confirm')
				}
				
			},

			onShare () {
				const { id, title, brief, banner_img } = this.rabbitGoodsInfo
				this.jump.appShare({
					title,
					des: brief,
					img: banner_img[0],
					path: `${this.routeTable.pBRabbitHeadGoodsDetail}?id=${id}`
				})
			}
		},
		
		onPullDownRefresh() {
			this.init()
		},
	}
</script>

<style scoped></style>
