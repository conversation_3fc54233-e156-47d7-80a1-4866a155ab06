// Vuex中的根目录state
const state = {
    requestPrefix: 'https://test-h5.wineyun.com', //接口请求前缀 (测试域名)
    ossPrefix: 'https://images.wineyun.com', //oss图片前缀（测试域名）
    agreementPrefix: 'https://test-h5.wineyun.com/agreement', //协议前缀（测试域名）
    dev: true,
  // requestPrefix: 'https://uh5.vinehoo.com', //接口请求前缀 (正式域名)
  // ossPrefix: 'https://images.vinehoo.com', //oss图片前缀（正式域名）
  // agreementPrefix: 'https://uh5.vinehoo.com/agreement', //协议前缀（正式域名）
  // dev: false,
  version: '1.0.2', //版本号
  from: '', //来自哪个端 '' = h5和微信小程序、1 = 安卓、2 = ios
  userInfo: {
    // "uid":"854735",
    // "telephone":"15923484576",
    // "nickname":"大团团子",
    // "avatar_image":"https://images.wineyun.com/vinehoo/client/user/avatar/854735.png",
    // "exps":"215",
    // "sex":"2",
    // "user_level":"1",
    // "user_level_name":"",
    // "is_pushconbtn":"1",
    // "is_pushsysbtn":"1",
    // "is_pushwlbtn":"1",
    // "is_pushcoubtn":"1",
    // "birthday":"2022-05-31",
    // "rabbit":"1",
    // "certified_info":"",
    // "created_time":"1653974527",
    // "collect_nums":"0",
    // "footprint_nums":"0",
    // "attention_nums":"0",
    // "fan_nums":"0",
    // "unpaid_nums":"0",
    // "paid_nums":"0",
    // "shipped_nums":"0",
    // "unporder_nums":"0",
    // "after_sale_nums":"0",
    // "coupon_totals":"3",
    // "conpon_expirings":"0",
    // "protocol":["1"],
    // "rabbit_available":12,
  }, //用户信息
  couponExpirationReminder: false, //优惠券到期提醒
  isCancelMineSignMask: 0, //是否取消过我的签到遮罩层 0 = 未取消过、1 = 取消过
  regionInfo: {}, //地区信息
  addressInfoState: {}, //地址信息（Vuex）
  labelList: ['家', '公司', '学校'], //标签列表
  invoiceInfo: {
    // id:1,
    // type_id:1,
    // invoice_name:'颠三倒四',
    // taxpayer:'',
    // telephone:'15923484576',
    // email:'<EMAIL>',
    // is_default:0
  }, //发票信息
  servercurrentTime: '', //服务器当前时间
  orderGoodsInfo: {
    is_cart: 0,
    submit_type: 0,
    coupons_id: 0,
    special_type: 0,
    group_id: 0,
    orderGoodsList: [
      // {
      // 	periods_type: 0,
      // 	is_cart: 0,
      // 	period: 266,
      // 	banner_img: 'https://images.wineyun.com/vinehoo/goods-images/20220524/1653360550515hGSxXmfmS_题图1.png',
      // 	title: '[测试-闪购]【有求必应 | 巴法大叔白空降】Ziobaffa Pinot Grigio Terre Siciliane IGT 2020 双支/六支套餐/十二支原箱',
      // 	package_name: '单支',
      // 	price: '0.01',
      // 	package_id: 259,
      // 	nums: 1,
      // 	express_type: 0,
      // 	predict_time: '2022-06-22',
      // 	goods_is_ts: 1,
      // 	is_checked_ts: false,
      // 	is_ts: 0,
      // 	is_cold_chain: 0,
      // }
    ],
  }, //订单商品信息
  payInfo: {
    // 门店
    payPlate: 0, //支付板块 0 = 普通商品，1 = 酒会，2 = 门店
    payment_amount: 2.01, //支付金额
    countdown: 86400, //倒计时
    main_order_no: '',
    order_type: 2,
    is_cross: 0,
  }, //支付信息
  afterSaleGoodsInfo: {
    // after_sale_status:1,
    // created_time: '2022-05-14 17:18:21',
    // express_number:'',
    // express_type:5,
    // goodsInfo:[
    // 	{
    // 		goods_img: 'https://images.wineyun.com/vinehoo/goods-images/主图框（750_464）.png',
    // 		goods_title:'2131321',
    // 		order_qty:1,
    // 		package_id:346,
    // 		package_name:'单支',
    // 		package_price: 0.01,
    // 		period: 423
    // 	}
    // ],
    // group_id:0,
    // group_last_num:0,
    // group_share_url:'',
    // order_no:'VHS220514000000018006',
    // order_type:0,
    // payment_amount:0.01,
    // predict_time:'2022-05-28 00:00:00',
    // status:2,
    // total_qty:1
  }, //售后商品信息
  winePartyCityInfo: {}, //城市信息
  winePartyOrderInfo: {
    // activity_time:'04.19(周二)00:00至04.30(周六)23:59',
    // activity_address:'重庆重庆市万州区万州区高笋塘广场',
    // wineparty_owner_uid:1,
    // coupon_id:0,
    // package_id:1007,
    // party_id:729,
    // winePartyList:[
    // 	{
    // 		thumb_image:'https://images.wineyun.com/vinehoo/vos/wineparty/tail_bg.png',
    // 		title:'团团创建的测试酒会',
    // 		money: 0.01,
    // 	}
    // ]
  }, //酒会订单信息
  rabbitOrderInfo: {
    // rabbitGoodsList: [
    // 	{
    // 		banner_img: 'https://images.wineyun.com/vinehoo/goods-images/1650335550118mmxNcKXpb_b.png',
    // 		title: '兔头商品123',
    // 		package_name: '单支',
    // 		nums: 1,
    // 		rabbit: 100,
    // 		period: 13,
    // 		package_id: 14,
    // 		predict_time: '2022-04-30',
    // 	}
    // ]
  }, //兔头订单信息
  rabbitNum: 2000, //兔头数量
  // rabbitCoupon: {} ,//兔头优惠券
  logisticsInfo: {
    // image:'https://images.wineyun.com/vinehoo/client/commodity/video/cover/il58_1652752586157.jpg?w=1280&h=630',
    // expressType:5,
    // logisticCode:'JDVD02388623231'
  }, //物流信息
  storeTableInfo: {}, //门店桌子信息
  storeInfo: {
    // "id":1,
    //    "store_name":"龙湖Moco",
    //    "store_img":[
    //        "https://img.vinehoo.com/osms/img/2021/12/********/包装.jpeg",
    //        "https://img.vinehoo.com/osms/img/2021/12/********/包装.jpeg"
    //    ],
    //    "address":"中华·两江春城春麓苑4幢",
    //    "start_time":"09:00",
    //    "end_time":"18:00",
    //    "store_phone":"***********",
    //    "status":1,
    //    "create_time":"2021-09-13 10:38",
    //    "longitude":"106.********",
    //    "latitude":"29.********",
    //    "sub_appid":"wx3e0b582d1f902659",
    //    "accounts":"**********",
    //    "service_fee":0,
    //    "is_invoice":1,
    //    "operator_id":1,
    //    "fee_type":1,
    //    "warehouse_code":"040",
    //    "warehouse_name":"酒云研酒所（上海吴兴路店）",
    //    "is_straight":0,
    //    "customer_code_retail":"C090005",
    //    "customer_name":"上海研酒所",
    //    "purchase_mode":[
    //        "1",
    //        "2",
    //        "3"
    //    ],
    //    "partner_id":null,
    //    "checkword":null,
    //    "monthly_card":null,
    //    "sender":null,
    //    "sender_tel":null,
    //    "fee_capping":0,
    //    "fee_section":"",
    //    "department_code":"23",
    //    "department_name":"酒云研酒所",
    //    "radius":500,
    //    "identification":"moco",
    //    "wifilocation":[
    //        "vh-3005-5G"
    //    ],
    //    "pay_method":1,
    //    "purchase_mode_list":[
    //        {
    //            "id":"1",
    //            "name":"物流配送"
    //        },
    //        {
    //            "id":"2",
    //            "name":"门店自提"
    //        },
    //        {
    //            "id":"3",
    //            "name":"门店场饮"
    //        }
    //    ]
  }, //门店信息
  storeOrderInfo: {
    order_source: 2,
    distribution_id: 3,
    storeOrderGoodsList: [
      {
        goods_image:
          'https://img.wine-talk.cn/test/osms/img/2022/03/20220330/3e3015a630a3a4ba9fd296afb4dbdf6a.png?s=833_833',
        is_cup: 0,
        nums: 1,
        goods_id: 2,
        pack_id: 115,
        goods_name: '[新增测试]吉特帕索特酒庄帕瑟诺 1998',
        c_name: '单只',
      },
    ],
  }, //门店订单信息
  storeOrderDetail: {}, //门店订单详情

  // 路由表
  routeTable: {
    // 主包(pages（主包）)
    pgMiniLiveRoom: 'plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin', //直播间
    pgIndex: '/pages/index/index', //首页
    pgFlashPurchase: '/pages/flash-purchase/flash-purchase', //闪购
    pgMiaoFa: '/pages/miaofa/miaofa', //秒发
    pgMiaoFaCard: '/pages/miaofa/cardDetail', //秒发
    pgMiaoFaCardTemporarily: '/pages/miaofa/cardDetailtemporarily',
    pgMine: '/pages/mine/mine', //我的
    pgLogin: '/pages/login/login', //登录
    pgGoodsDetail: '/pages/goods-detail/goods-detail', //商品详情
    pgStoreGoodsDetail: '/pages/store-goods-detail/store-goods-detail', //门店商品详情

    // 子包A(packageB(首页相关))
    pANewsDetail: '/packageA/pages/news-detail/news-detail', //快报详情
    pAMoreThanWine: '/packageA/pages/more-than-wine/more-than-wine', //不只有酒（卡片点击更多跳转的页面）

    // 子包B(packageB(商城相关))
    pBAfterSaleDetail: '/packageB/pages/after-sale-detail/after-sale-detail', //售后详情
    pBAfterSaleGoodsService: '/packageB/pages/after-sale-goods-service/after-sale-goods-service', //售后服务类型
    pBLogisticsDetail: '/packageB/pages/logistics-detail/logistics-detail', //物流详情
    pBOrderDeposit: '/packageB/pages/order-deposit/order-deposit', //订金订单
    pBOrderDepositDetail: '/packageB/pages/order-deposit-detail/order-deposit-detail', //订金订单详情
    pBOrderDetail: '/packageB/pages/order-detail/order-detail', //订单详情页
    pBOrderInvoice: '/packageB/pages/order-invoice/order-invoice', //订单可开票列表
    pBOrderInvoiceHistotyList: '/packageB/pages/order-invoice-history-list/order-invoice-history-list', //开票历史列表
    pBOrderInvoiceHistotyDetail: '/packageB/pages/order-invoice-history-detail/order-invoice-history-detail', //开票历史详情
    pBOrderInvoiceBrowse: '/packageB/pages/order-invoice-browse/order-invoice-browse', //发票预览
    pBPayment: '/packageB/pages/payment/payment', //支付页面
    pBPaySuccess: '/packageB/pages/pay-success/pay-success', //支付成功页面
    pBRabbitHeadGoodsDetail: '/packageB/pages/rabbit-head-goods-detail/rabbit-head-goods-detail',
    pBRabbitExchangeSuccess: '/packageB/pages/rabbit-exchange-success/rabbit-exchange-success', //兔头商品兑换成功
    pBRabbitHeadOrderDetail: '/packageB/pages/rabbit-head-order-detail/rabbit-head-order-detail', //兔头商店订单详情
    pBRabbitHeadShop: '/packageB/pages/rabbit-head-shop/rabbit-head-shop', //兔头商店
    pBSecondHairSecond: '/packageB/pages/second-hair-second/second-hair-second', //秒发二级页面
    pBShoppingCart: '/packageB/pages/shopping-cart/shopping-cart', //购物车
    pBOrderConfirm: '/packageB/pages/order-confirm/order-confirm', //订单确认
    // 门店
    pBStoreShoppingCart: '/packageB/pages/store-shopping-cart/store-shopping-cart', //门店购物车
    pBStoreGoodsSearch: '/packageB/pages/store-goods-search/store-goods-search', //门店搜索
    pBStoreOrder: '/packageB/pages/store-order/store-order', //门店订单
    pBStoreHistoryInvoice: '/packageB/pages/store-history-invoice/store-history-invoice', //门店历史发票
    pBStoreOrderInvoice: '/packageB/pages/store-order-invoice/store-order-invoice', //门店订单开票

    pCWineCommentSend: '/packageC/pages/wine-comment-send/wine-comment-send',
    pCWineComment: '/packageC/pages/wine-comment/wine-comment',
    pCWineCommentDetail: '/packageC/pages/wine-comment-detail/wine-comment-detail',
    pCTopicMore: '/packageC/pages/more-topic-list/more-topic-list',
    pCTopicDetail: '/packageC/pages/topic-detail/topic-detail',
    PCTopicSelectMore: '/packageC/pages/topic-more/topic-more',

    pCSendPost: '/packageC/pages/send-post/send-post',
    pCMyPost: '/packageC/pages/my-post/my-post',

    // 子包D（packageD（资讯相关：酒闻、酒会））
    pDWinePartyBoutique: '/packageD/pages/wine-party-boutique/wine-party-boutique', //酒会列表
    pDWinePartyDetail: '/packageD/pages/wine-party-detail/wine-party-detail', //酒会详情
    pDWineSmellDetail: '/packageD/pages/wine-smell-detail/wine-smell-detail', //酒闻详情
    pDWinePartyOrderList: '/packageD/pages/wine-party-order-list/wine-party-order-list', //酒会订单列表

    // 子包E(packageE(用户相关))
    pEAddressAdd: '/packageE/pages/address-add/address-add', //新增地址
    pEAddressManagement: '/packageE/pages/address-management/address-management', //地址管理
    pEAddressNewLabel: '/packageE/pages/address-new-label/address-new-label', //新建标签
    pECertificationApply: '/packageE/pages/certification-apply/certification-apply', //申请认证
    pECertificationDetail: '/packageE/pages/certification-detail/certification-detail', //认证详情
    pECouponList: '/packageE/pages/coupon-list/coupon-list', //优惠券列表
    pECouponHistory: '/packageE/pages/coupon-history/coupon-history', //优惠券列表
    pEDailyTasks: '/packageE/pages/daily-tasks/daily-tasks', //每日任务
    pEInvoiceHeadAdd: '/packageE/pages/invoice-head-add/invoice-head-add', //添加发票抬头
    pEInvoiceMangement: '/packageE/pages/invoice-mangement/invoice-mangement', //发票管理
    pEMessageCenter: '/packageE/pages/message-center/message-center', //消息中心
    pELargeTurntablePrizeRecord: '/packageE/pages/large-turntable-prize-record/large-turntable-prize-record', //大转盘中奖记录
    pEMyCollection: '/packageE/pages/my-collection/my-collection', //我的收藏
    pEMyGrade: '/packageE/pages/my-grade/my-grade', //我的等级
    pEMyOrder: '/packageE/pages/my-order/my-order', //我的订单
    pEUserInfo: '/packageE/pages/user-info/user-info', //用户信息
    pEUpdateNickname: '/packageE/pages/update-nickname/update-nickname', //修改昵称
    pEWishList: '/packageE/pages/wish-list/wish-list', //心愿清单

    // 子包F(pakageF（工具页面）)
    pFGlobalSearch: '/packageF/pages/global-search/global-search', //全局搜索
    pFWebView: '/packageF/pages/web-view/web-view', //web-view页面
    //子包G
    pGLargeTurntablePrizeRecord: '/packageG/pages/large-turntable-prize-record/large-turntable-prize-record', //新人大转盘中奖记录

    pHAuctionIndex: '/packageH/pages/auction-index/auction-index',
    pHAuctionMine: '/packageH/pages/auction-mine/auction-mine',
    pHAuctionGoodsCreate: '/packageH/pages/auction-goods-create/auction-goods-create',
    pHAuctionGoodsCreateStep: '/packageH/pages/auction-goods-create-step/auction-goods-create-step',
    pHAuctionGoodsDrafts: '/packageH/pages/auction-goods-drafts/auction-goods-drafts',
    pHAuctionGoodsList: '/packageH/pages/auction-goods-list/auction-goods-list',
    pHAuctionGoodsDetail: '/packageH/pages/auction-goods-detail/auction-goods-detail',
    pHAuctionBidRecords: '/packageH/pages/auction-bid-records/auction-bid-records',
    pHAuctionMsg: '/packageH/pages/auction-msg/auction-msg',
    pHAuctionMsgDetail: '/packageH/pages/auction-msg-detail/auction-msg-detail',
    pHAuctionEnjoy: '/packageH/pages/auction-enjoy/auction-enjoy',
    pHAuctionRemind: '/packageH/pages/auction-remind/auction-remind',
    pHAuctionRemindFromMine: '/packageH/pages/auction-remind/auction-remind?isFromMine=1',
    pHAuctionCertificateList: '/packageH/pages/auction-certificate-list/auction-certificate-list',
    pHAuctionCertificateListFromMine: '/packageH/pages/auction-certificate-list/auction-certificate-list?isFromMine=1',
    pHAuctionRealName: '/packageH/pages/auction-real-name/auction-real-name',
    pHAuctionRNPersonal: '/packageH/pages/auction-rn-personal/auction-rn-personal',
    pHAuctionRNPersonalNew: '/packageH/pages/auction-rn-personal-new/auction-rn-personal-new',
    pHAuctionRNCompany: '/packageH/pages/auction-rn-company/auction-rn-company',
    pHAuctionFundsList: '/packageH/pages/auction-funds-list/auction-funds-list',
    pHAuctionFundsDetail: '/packageH/pages/auction-funds-detail/auction-funds-detail',
    pHAuctionFundsListNew: '/packageH/pages/auction-funds-list-new/auction-funds-list-new',
    pHAuctionFundsDetailNew: '/packageH/pages/auction-funds-detail-new/auction-funds-detail-new',
    pHAuctionBankList: '/packageH/pages/auction-bank-list/auction-bank-list',
    pHAuctionBankAdd: '/packageH/pages/auction-bank-add/auction-bank-add',
    pHAuctionPayeeAccount: '/packageH/pages/auction-payee-account/auction-payee-account',
    pHAuctionPayeeAccountEdit: '/packageH/pages/auction-payee-account-edit/auction-payee-account-edit',
    pHAuctionEarnestList: '/packageH/pages/auction-earnest-list/auction-earnest-list',
    pHAuctionEarnestDetail: '/packageH/pages/auction-earnest-detail/auction-earnest-detail',
    pHAuctionMyGoodsList: '/packageH/pages/auction-my-goods-list/auction-my-goods-list',
    pHAuctionMyGoodsDetail: '/packageH/pages/auction-my-goods-detail/auction-my-goods-detail',
    pHAuctionSearch: '/packageH/pages/auction-search/auction-search',
    pHAuctionCreditValues: '/packageH/pages/auction-credit-values/auction-credit-values',
    pHAuctionCreditValuesFromMine: '/packageH/pages/auction-credit-values/auction-credit-values?isFromMine=1',
    pHAuctionCreditValuesIntro: '/packageH/pages/auction-credit-values-intro/auction-credit-values-intro',
    pHAuctionBuyerOrderDetail: '/packageH/pages/auction-buyer-order-detail/auction-buyer-order-detail', //买家订单详情
    pHAuctionOrderDetail: '/packageH/pages/auction-order-detail/auction-order-detail', //拍卖订单详情
    pHAuctionSellerOrderDetail: '/packageH/pages/auction-seller-order-detail/auction-seller-order-detail', //卖家订单详情
    pHAuctionBuyerAfterSaleDetail: '/packageH/pages/auction-buyer-after-sale-detail/auction-buyer-after-sale-detail', //买家售后详情
    pHAuctionSellerAfterSaleDetail: '/packageH/pages/auction-seller-after-sale-detail/auction-seller-after-sale-detail', //卖家售后详情
    pHAuctionOrderEvaluate: '/packageH/pages/auction-order-evaluate/auction-order-evaluate', //订单评价
    pHAuctionOrderEvaluateNew: '/packageH/pages/auction-order-evaluate-new/auction-order-evaluate-new', //拍卖订单评价（新）
    pHAuctionOrderEvaluateList: '/packageH/pages/auction-order-evaluate-list/auction-order-evaluate-list', //订单评价列表
    pHAuctionSelectService: '/packageH/pages/auction-select-service/auction-select-service', //选择服务
    pHAuctionLogisticsInfo: '/packageH/pages/auction-logistics-info/auction-logistics-info', //物流信息
    pHAuctionAfterSaleProgress: '/packageH/pages/auction-after-sale-progress/auction-after-sale-progress', //售后进度
    pHAuctionBuyerOrderList: '/packageH/pages/auction-buyer-order-list/auction-buyer-order-list',
    pHAuctionSellerOrderList: '/packageH/pages/auction-seller-order-list/auction-seller-order-list',
    pHAuctionBuyerAfterSaleList: '/packageH/pages/auction-buyer-after-sale-list/auction-buyer-after-sale-list',
    pHAuctionSellerAfterSaleList: '/packageH/pages/auction-seller-after-sale-list/auction-seller-after-sale-list',
    pHAuctionMyParticipation: '/packageH/pages/auction-my-participation/auction-my-participation', //我的参拍
    pHAuctionAdDetail: '/packageH/pages/auction-ad-detail/auction-ad-detail',
    pHAuctionMyBuyingList: '/packageH/pages/auction-my-buying-list/auction-my-buying-list',
    pHAuctionGoodsCreateNew: '/packageH/pages/auction-goods-create-new/auction-goods-create-new',
    pHAuctionEntrustEarnestPay: '/packageH/pages/auction-entrust-earnest-pay/auction-entrust-earnest-pay',
    pHAuctionEntrustEarnestPaySuccess:
      '/packageH/pages/auction-entrust-earnest-pay-success/auction-entrust-earnest-pay-success',
    pHAuctionMyCreateList: '/packageH/pages/auction-my-create-list/auction-my-create-list',
    pHAuctionMyCreateListSearch: '/packageH/pages/auction-my-create-list-search/auction-my-create-list-search',
    pHAuctionMyCreateDrafts: '/packageH/pages/auction-my-create-drafts/auction-my-create-drafts',
    pHAuctionNavigation: '/packageH/pages/auction-navigation/auction-navigation',
    pPaySuccessJump: '/pages/pay-success-jump/pay-success-jump',

    // 子包F(pakageI（学堂）)
    PICourse: '/packageI/pages/course/index',
    PICourseDetails: '/packageI/pages/course/details/details',
    PITestList: '/packageI/pages/school-test/index',
    PITestDetails: '/packageI/pages/school-test/test-detail',
    PIAnswerList: '/packageI/pages/school-test/answer-list',
    PITestFinish: '/packageI/pages/school-test/answer-finish',
    PITestErrorQuestionList: '/packageI/pages/school-test/errorQuestionList',
    PITestErrorQuestionDetails: '/packageI/pages/school-test/questionDetail',
    PIFavoritesIndex: '/packageI/pages/my-favorites/index',
    PIFavoritesQuestion: '/packageI/pages/my-favorites/favoritesQuestion',
    PILearningRecords: '/packageI/pages/learning-records/index',
    PICertList: '/packageI/pages/my-certificate/index',
    PICertDetails: '/packageI/pages/my-certificate/details',
    //余额
    pJMyBalance: '/packageJ/pages/my-balance/index', //我的余额
    pJBalanceRecharge: '/packageJ/pages/balance-recharge/balance-recharge', //我的余额
    pJCardExchange: '/packageJ/pages/card-exchange/card-exchange',
     pJGiftCard: '/packageJ/pages/gift-card/gift-card'
    
  }, //路由
}

export default state
