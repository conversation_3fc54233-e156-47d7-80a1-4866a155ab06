<template>
  <view class="content" :class="loading ? 'h-vh-100 o-hid' : ''" style="height: 100vh; overflow: hidden">
    <scroll-view
      scroll-y
      @scroll="handleGoodsScroll"
      style="height: 100%"
      :scroll-top="scrollTop"
      @scrolltolower="handleScrollToLower"
    >
      <!-- 导航栏 -->
      <view id="index-navbar" class="">
        <view v-if="from == ''" class="fade-in">
          <vh-navbar
            height="55"
            :is-back="false"
            :background="{ background: navbarBackground }"
            navbarClass="grayscale-100"
          >
            <view class="d-flex j-sb a-center w-p100">
              <image
                class="w-132 h-48 ml-24"
                :src="ossIcon(navbarIconObj.logo)"
                @click="jump.loginNavigateTo(routeTable.pBRabbitHeadShop)"
              />
              <view class="flex-c-c mr-24">
                <view class="flex-c-c mr-26 wh-36">
                  <view class="d-flex">
                    <image
                      class="wh-36 p-10"
                      :src="ossIcon(`/second_hair/s_sea_white_36.png`)"
                      @click="openGlobalSearch"
                    />
                  </view>
                </view>
                <view class="flex-c-c mr-26 wh-36">
                  <view class="p-rela d-flex" @click="openShoppingCar">
                    <image class="wh-36 p-10" :src="ossIcon(`/second_hair/s_car_white_36.png`)" />
                    <view
                      v-if="shoppingCartNum"
                      class="p-abso top-n-02 right-n-02 w-26 h-26 bg-f7ec74 b-rad-p50 flex-c-c font-16 text-cc7a03"
                      >{{ shoppingCartNum }}</view
                    >
                  </view>
                </view>
                <view class="flex-c-c wh-36" @click="openMessageCenter">
                  <view class="p-rela d-flex">
                    <image class="w-36 h-38 p-10" :src="ossIcon(`/second_hair/s_not_white_36_38.png`)" />
                    <view v-if="unReadTotalNum" class="p-abso top-10 right-10 wh-10 bg-f7ec74 b-rad-p50" />
                  </view>
                </view>
              </view>
            </view>
          </vh-navbar>
        </view>
        <view v-else class="fade-in">
          <view class="p-fixed z-980 top-0 w-p100 grayscale-100" :style="{ background: navbarBackground }">
            <view :style="{ height: appStatusBarHeight + 'px' }"></view>
            <view class="h-px-46 d-flex j-sb a-center w-p100">
              <image
                class="w-132 h-48 p-20"
                :src="ossIcon(navbarIconObj.logo)"
                @click="jump.appAndMiniJump(0, routeTable.pBRabbitHeadShop, from, 0, true)"
              />
              <view class="flex-c-c mr-24">
                <view class="flex-c-c mr-26 wh-36">
                  <view class="d-flex">
                    <image
                      class="wh-36 p-10"
                      :src="ossIcon(`/second_hair/s_sea_white_36.png`)"
                      @click="openGlobalSearch"
                    />
                  </view>
                </view>
                <view class="flex-c-c mr-26 wh-36">
                  <view class="p-rela d-flex" @click="openShoppingCar">
                    <image class="wh-36 p-10" :src="ossIcon(`/second_hair/s_car_white_36.png`)" />
                    <view
                      v-if="shoppingCartNum"
                      class="p-abso top-n-02 right-n-02 w-26 h-26 bg-f7ec74 b-rad-p50 flex-c-c font-16 text-cc7a03"
                      >{{ shoppingCartNum }}</view
                    >
                  </view>
                </view>
                <view class="flex-c-c wh-36" @click="openMessageCenter">
                  <view class="p-rela d-flex">
                    <image class="w-36 h-38 p-10" :src="ossIcon(`/second_hair/s_not_white_36_38.png`)" />
                    <view v-if="unReadTotalNum" class="p-abso top-10 right-10 wh-10 bg-f7ec74 b-rad-p50" />
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 数据内容 -->
      <view v-if="!loading" id="outer-content">
        <!-- banner -->
        <view class="p-abso top-0 w-p100 h-752 grayscale-100">
          <image class="w-p100 h-752" :src="`${osip}/index/ban.png`" />
        </view>

        <!-- 轮播图 -->
        <view
          class="p-rela ml-24 mr-24 grayscale-100"
          :style="{ paddingTop: from == '' ? '60rpx' : parseInt(appStatusBarHeight) + 51 + 'px' }"
        >
          <view class="b-sh-02021200-015 b-rad-10 o-hid">
            <vh-swiper
              :list="swiperList"
              :height="344"
              @click="jump.pubConfJumpBD($event, 2, 3, 101000, $event.id, from)"
            />
          </view>
        </view>

        <!-- 内容板块 -->
        <view class="p-rela z-01 grayscale-100">
          <!-- 酒云网小程序简介（官方商城、正品保障...） -->
          <view
            class="mt-20 d-flex j-sb j-center pl-46 pr-46"
            @click="jump.h5Jump(`${agreementPrefix}/aboutVineHoo`, from, 4)"
          >
            <view class="d-flex a-center" v-for="(item, index) in briefList" :key="index">
              <image class="w-26 h-26" :src="`${osip}/index/bre_${item.imgType}.png`" mode="aspectFill" />
              <text class="font-20 text-3">{{ item.name }}</text>
            </view>
          </view>

          <!-- 配置项内容（社区、研酒所、拍照识酒） -->
          <view class="p-rela bg-ffffff b-rad-10 mt-32 ml-24 mr-24">
            <!-- 金刚区 -->
            <IndexGoldAreaList :from="from" :list="goldAreaList" />

            <!-- 快报 -->
            <view v-if="bulletinList.length" class="ml-24 mr-24 pb-24">
              <view class="bg-li-11 d-flex a-center b-rad-10 ptb-14-plr-24">
                <image
                  class="w-66 h-36"
                  src="https://images.vinehoo.com/vinehoomini/v3/index/fast_news.png"
                  mode="aspectFill"
                />
                <view class="w-01 h-28 bg-dddddd ml-18" />
                <view class="h-34 flex-1 ml-10 o-hid">
                  <swiper
                    class="h-p100"
                    :circular="true"
                    :autoplay="true"
                    :interval="3000"
                    :duration="1000"
                    vertical="true"
                  >
                    <swiper-item v-for="(item, index) in bulletinList" :key="index">
                      <view
                        class="d-flex a-center h-p100 font-24 text-3 text-hidden-1"
                        @click="jump.appAndMiniJump(2, `${routeTable.pANewsDetail}?id=${item.id}`, from)"
                        >{{ item.title }}</view
                      >
                    </swiper-item>
                  </swiper>
                </view>
              </view>
            </view>
          </view>

          <!-- 实时专区(胶囊) -->
          <view v-if="newPeopleCapsuleList.concat(capsuleList).length" class="mt-20 ml-24 mr-24">
            <vh-swiper
              :list="newPeopleCapsuleList.concat(capsuleList)"
              mode="rect"
              :loadingType="9"
              :height="206"
              bg-color="transparent"
              @click="jump.pubConfJumpBD($event, 2, 3, $event.type === 7 ? 109000 : 103000, $event.id, from)"
            />
          </view>

          <!-- 活动板块 -->
          <view v-if="activityList.length" class="d-flex flex-column j-center a-center">
            <!-- 活动有四个 -->
            <view v-if="activityList.length == 4" class="w-702 h-236 bg-ffffff b-rad-10 mt-20">
              <image class="p-abso w-702 h-236 b-rad-10" :src="`${osip}/index/act_four_new.png`" mode="aspectFill" />
              <view class="p-rela z-02 d-flex j-sb p-24">
                <view class="w-146" v-for="(item, index) in activityList" :key="item.id" @click="JumpPage(item)">
                  <view v-if="!item.is_colum" class="mb-10 font-24 font-wei text-3 text-hidden-1">{{
                    item.activity_name
                  }}</view>
                  <!-- <view class="mt-10 mb-16 font-18 text-6 text-hidden-1">{{item.sub_activity_name}}</view> -->
                  <vh-image
                    :loading-type="4"
                    v-if="!item.is_colum"
                    :src="item.image"
                    :width="146"
                    :height="146"
                    :border-radius="8"
                  />
                  <view v-if="item.is_colum" class="mb-10 font-24 font-wei text-3 text-hidden-1">{{ item.name }}</view>
                  <!-- <view class="mt-10 mb-16 font-18 text-6 text-hidden-1">{{item.sub_activity_name}}</view> -->
                  <vh-image
                    :loading-type="4"
                    v-if="item.is_colum"
                    :src="item.icon"
                    :width="146"
                    :height="146"
                    :border-radius="8"
                  />
                </view>
              </view>
            </view>

            <!-- 活动有三个 -->
            <view v-if="activityList.length == 3" class="w-702 h-306 bg-ffffff b-rad-10 mt-20">
              <image class="p-abso w-702 h-306 b-rad-10" :src="`${osip}/index/act_three_new.png`" mode="aspectFill" />
              <view class="p-rela z-02 d-flex j-sb p-24">
                <view class="w-202" v-for="(item, index) in activityList" :key="item.id" @click="JumpPage(item)">
                  <view v-if="!item.is_colum" class="mb-16 font-28 font-wei text-3 l-h-40 text-hidden-1">{{
                    item.activity_name
                  }}</view>
                  <!-- <view class="mt-10 mb-16 font-24 text-6 text-hidden-1">{{item.sub_activity_name}}</view> -->
                  <vh-image
                    v-if="!item.is_colum"
                    :loading-type="4"
                    :src="item.image"
                    :width="202"
                    :height="202"
                    :border-radius="10"
                  />
                  <view v-if="item.is_colum" class="mb-16 font-28 font-wei text-3 l-h-40 text-hidden-1">{{
                    item.name
                  }}</view>
                  <!-- <view class="mt-10 mb-16 font-24 text-6 text-hidden-1">{{item.sub_activity_name}}</view> -->
                  <vh-image
                    :loading-type="4"
                    v-if="item.is_colum"
                    :src="item.icon"
                    :width="202"
                    :height="202"
                    :border-radius="10"
                  />
                </view>
              </view>
            </view>

            <!-- 活动有两个 -->
            <view v-if="activityList.length == 2" class="w-702 h-172 bg-ffffff b-rad-10 mt-20">
              <image class="p-abso w-702 h-172 b-rad-10" :src="`${osip}/index/act_two_new.png`" mode="aspectFill" />
              <view class="p-rela z-02 d-flex j-sb p-24">
                <view
                  class="w-314 h-124 d-flex"
                  v-for="(item, index) in activityList"
                  :key="item.id"
                  @click="JumpPage(item)"
                >
                  <vh-image
                    v-if="!item.is_colum"
                    :loading-type="4"
                    :src="item.image"
                    :width="124"
                    :height="124"
                    :border-radius="6"
                  />
                  <view class="w-190 h-124" v-if="!item.is_colum">
                    <view class="p-20">
                      <view class="font-24 font-wei text-3 text-hidden-1">{{ item.activity_name }}</view>
                      <view class="mt-14 font-18 text-6 text-hidden-2">{{ item.sub_activity_name }}</view>
                    </view>
                  </view>
                  <vh-image
                    v-if="item.is_colum"
                    :loading-type="4"
                    :src="item.icon"
                    :width="124"
                    :height="124"
                    :border-radius="6"
                  />
                  <view class="w-190 h-124" v-if="item.is_colum">
                    <view class="p-20">
                      <view class="font-24 font-wei text-3 text-hidden-1">{{ item.name }}</view>
                      <view class="mt-14 font-18 text-6 text-hidden-2">{{ item.name }}</view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 新人福利专区 -->
          <view v-if="false" class="d-flex j-center a-center mt-20">
            <view class="p-rela w-700 h-406">
              <image class="p-abso w-700 h-406" :src="`${osip}/index/new_peo_bg.png`" mode="aspectFill" />
              <image
                class="p-abso z-04 left-0 bottom-116 w-56 h-86"
                :src="`${osip}/index/new_peo_coin.png`"
                mode="aspectFill"
              />
              <view class="p-rela z-02 d-flex j-sb a-center mt-22 ml-24 mr-20">
                <view class="d-flex a-center">
                  <text class="font-32 text-ffdeb1 l-h-44">新人福利专区</text>
                  <text class="ml-14 font-24 text-ffffff">领专属红包</text>
                </view>
                <image class="w-120 h-46" :src="`${osip}/index/new_peo_go.png`" mode="widthFix" />
              </view>
              <view class="p-rela z-02 d-flex a-center ml-16 mr-20 o-scr-x">
                <view class="mt-12 ml-04" v-for="(item, index) in 5" :key="index">
                  <view class="p-rela w-190 h-292 b-rad-08 o-hid ml-08">
                    <image class="p-abso w-190 h-292" :src="`${osip}/index/new_peo_goods_bg.png`" mode="aspectFill" />
                    <view class="p-rela z-03 w-190 h-166">
                      <vh-image :loading-type="4" :src="item.banner_img" :height="166" />
                    </view>
                    <view class="p-rela z-03 ml-08 mt-12">
                      <view class="font-18 text-3 text-hidden-1">"绅士"公牛戈兰红…</view>
                      <view class="">
                        <text class="font-32 font-wei text-e80404"><text class="font-18">¥</text>128</text>
                        <text class="ml-10 font-20 text-9 text-dec-l-t">¥259</text>
                      </view>
                      <view class="">
                        <text class="bg-e80404 b-rad-02 ptb-02-plr-12 font-18 text-ffffff">直降 ¥131</text>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 等级专属 -->
          <view v-if="false" class="d-flex j-center a-center mt-20">
            <view class="w-702 h-440">
              <image class="p-abso w-702 h-440" :src="`${osip}/index/lv_low_bg.png`" mode="aspectFill" />
              <view class="p-rela z-02 d-flex j-sb a-center mt-12 ml-24 mr-20">
                <view class="d-flex a-center">
                  <text class="font-36 font-wei text-ffffff l-h-50">Lv2等级专属</text>
                  <text class="mt-04 ml-04 font-24 text-ffffff l-h-34">尊享等级福利</text>
                </view>
                <view class="mt-14">
                  <text class="font-24 text-7a5400">更多</text>
                  <u-icon name="arrow-right" :size="24" color="#7a5400" />
                </view>
              </view>
              <view class="p-rela z-02 d-flex a-center mt-32 ml-16 mr-20 o-scr-x">
                <view class="mt-12 ml-04" v-for="(item, index) in 5" :key="index">
                  <view class="p-rela w-224 h-316 b-rad-08 o-hid ml-08">
                    <image class="p-abso w-224 h-316" :src="`${osip}/index/lv_low_goods_bg.png`" mode="aspectFill" />
                    <view class="p-rela z-03 h-166 d-flex j-center a-center">
                      <image class="w-40 h-148" :src="`${osip}/index/new_peo_goods_bg.png`" mode="aspectFill" />
                    </view>

                    <view class="p-rela z-03 ml-08 mt-12">
                      <view class="font-18 text-3 text-hidden-2">
                        <text class="bg-cda241 ptb-02-plr-08 b-rad-02 font-16 text-ffffff">Lv2</text>
                        <text>"绅士"公牛戈兰红戈兰红戈兰端午节不到位的文档无多的地位多</text>
                      </view>
                      <view class="mt-16">
                        <text class="font-32 font-wei text-e80404"><text class="font-18">¥</text>128</text>
                        <text class="ml-04 font-20 text-9 text-dec-l-t">¥259</text>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 平铺的卡片列表 -->
          <view v-if="tileCardList.length" class="">
            <view class="d-flex j-sb flex-wrap mt-06 mr-24 ml-24">
              <block class="" v-for="(item, index) in tileCardList" :key="index">
                <!-- 特卖 -->
                <view
                  v-if="item.style == 4 && item.card_extend_detail.length"
                  class="p-rela w-346 h-308 b-rad-10 o-hid mt-14"
                >
                  <image class="p-abso w-346 h-308" :src="`${osip}/index/fri_sale_day.png`" mode="aspectFill" />

                  <view class="p-rela z-02 d-flex a-center mt-24 ml-24">
                    <view class="">
                      <vh-image
                        v-if="item.card_class == 2"
                        :loading-type="2"
                        :src="item.card_name"
                        :width="126"
                        :height="32"
                        mode="aspectFit"
                      />
                      <view v-else class="font-32 font-wei text-3 l-h-32">{{ item.card_name }}</view>
                    </view>
                    <view class="p-rela w-152 h-34 ml-08">
                      <image class="p-abso w-152 h-34 ml-08" :src="item.sub_title_back" />
                      <view
                        style="line-height: 1"
                        class="p-rela z-02 w-100 h-34 d-flex j-center a-center ml-54 font-24 text-2e7bff w-s-now"
                        >{{ item.sub_title }}</view
                      >
                    </view>
                  </view>

                  <swiper
                    :autoplay="rowCardSwiperConfig.autoplay"
                    :interval="saleSwiperInterval"
                    :circular="rowCardSwiperConfig.circular"
                    :indicator-dots="rowCardSwiperConfig.indicatorDots"
                    :disable-touch="rowCardSwiperConfig.disableTouch"
                  >
                    <swiper-item v-for="(inItem, inIndex) in item.card_extend_detail" :key="inIndex">
                      <view
                        class="sale-swiper-item w-298 h-196 d-flex j-center a-center b-rad-08 mt-24 ml-24"
                        @click="
                          jump.appAndMiniJumpBD(
                            1,
                            `${routeTable.pgGoodsDetail}?id=${inItem.id}`,
                            2,
                            2,
                            105000,
                            inItem.id,
                            from
                          )
                        "
                      >
                        <view class="ml-12">
                          <vh-image
                            :loading-type="3"
                            :src="inItem.product_img"
                            :width="148"
                            :height="148"
                            mode="aspectFit"
                          />
                        </view>

                        <view class="ml-12" style="width: 114rpx">
                          <view class="mb-12 h-min-64 font-22 text-3 text-hidden-2 l-h-32">{{ inItem.title }}</view>
                          <text
                            style="max-width: 100%; vertical-align: top"
                            class="bg-fde6c9 b-rad-04 ptb-02-plr-08 font-18 text-b46f3a d-inline-block text-hidden"
                            >{{ inItem.sub_title }}</text
                          >
                          <view class="mt-12">
                            <text
                              v-if="inItem.is_hidden_price || [3, 4].includes(inItem.onsale_status)"
                              class="font-32 font-wei text-e80404"
                              >价格保密</text
                            >
                            <template v-else>
                              <text class="font-32 font-wei text-e80404"
                                ><text class="font-18">¥</text>{{ inItem.price }}</text
                              >
                            </template>
                          </view>
                        </view>
                      </view>
                    </swiper-item>
                  </swiper>
                </view>

                <!-- 秒杀 -->
                <view
                  v-if="item.style == 5 && item.card_extend_detail.length"
                  class="bg-ffffff w-346 h-308 b-rad-10 o-hid mt-14"
                >
                  <view class="d-flex a-center mt-24 ml-24">
                    <view class="">
                      <vh-image
                        v-if="item.card_class == 2"
                        :loading-type="2"
                        :src="item.card_name"
                        :width="126"
                        :height="32"
                        mode="aspectFit"
                      />
                      <view v-else class="font-32 font-wei text-3 l-h-32">{{ item.card_name }}</view>
                    </view>
                    <view class="p-rela w-152 h-34 ml-08">
                      <image class="p-abso w-152 h-34 ml-08" :src="item.sub_title_back" />
                      <view class="p-rela z-02 w-100 h-34 d-flex j-center a-center ml-54 font-24 text-ff253d w-s-now">
                        <view v-if="seckillOpenSaleSeconds == 0" class="">{{
                          item.sub_title ? item.sub_title : '秒杀中！'
                        }}</view>
                        <vh-count-down
                          v-else
                          :show-days="false"
                          :timestamp="seckillOpenSaleSeconds"
                          :has-separator-distance="false"
                          bg-color="transparent"
                          :font-size="20"
                          :separator-size="20"
                          color="#FF253D"
                        />
                      </view>
                    </view>
                  </view>

                  <swiper
                    :autoplay="rowCardSwiperConfig.autoplay"
                    :interval="seckillSwiperInterval"
                    :circular="rowCardSwiperConfig.circular"
                    :indicator-dots="rowCardSwiperConfig.indicatorDots"
                    :disable-touch="rowCardSwiperConfig.disableTouch"
                  >
                    <swiper-item v-for="(inItem, inIndex) in item.card_extend_detail" :key="inIndex">
                      <view
                        class="w-298 h-196 bg-li-10 d-flex j-center a-center b-rad-08 mt-24 ml-24"
                        @click="
                          jump.appAndMiniJumpBD(
                            1,
                            `${routeTable.pgGoodsDetail}?id=${inItem.id}`,
                            2,
                            2,
                            105000,
                            inItem.id,
                            from
                          )
                        "
                      >
                        <view class="ml-12">
                          <vh-image
                            :loading-type="3"
                            :src="inItem.product_img"
                            :width="148"
                            :height="148"
                            mode="aspectFit"
                          />
                        </view>
                        <view class="p-rela z-02 ml-12 o-hid" style="width: 114rpx">
                          <view class="mb-12 h-min-64 font-22 text-3 text-hidden-2 l-h-32">{{ inItem.title }}</view>
                          <text
                            style="max-width: 100%; vertical-align: top"
                            class="bg-fde6c9 b-rad-04 ptb-02-plr-08 font-18 text-e80404 d-inline-block text-hidden"
                            >{{ inItem.sub_title }}</text
                          >
                          <view class="mt-12">
                            <text
                              v-if="inItem.is_hidden_price || [3, 4].includes(inItem.onsale_status)"
                              class="font-32 font-wei text-e80404"
                              >价格保密</text
                            >
                            <template v-else>
                              <text class="font-32 font-wei text-e80404"
                                ><text class="font-18">¥</text>{{ inItem.price }}</text
                              >
                            </template>
                          </view>
                        </view>
                      </view>
                    </swiper-item>
                  </swiper>
                </view>

                <!-- 拼团 -->
                <view
                  v-if="item.style == 6 && item.card_extend_detail.length"
                  class="bg-ffffff w-346 h-308 b-rad-10 o-hid mt-14 pl-24 pr-24"
                >
                  <view class="d-flex a-center mt-24">
                    <view class="">
                      <vh-image
                        v-if="item.card_class == 2"
                        :loading-type="2"
                        :src="item.card_name"
                        :width="126"
                        :height="32"
                        mode="aspectFit"
                      />
                      <view v-else class="font-32 font-wei text-3 l-h-32">{{ item.card_name }}</view>
                    </view>
                    <view class="p-rela w-152 h-34 ml-08">
                      <image class="p-abso w-152 h-34 ml-08" :src="item.sub_title_back" />
                      <view class="p-rela z-02 w-100 h-34 d-flex j-center a-center ml-54 font-24 text-cb7700 w-s-now">{{
                        item.sub_title
                      }}</view>
                    </view>
                  </view>
                  <view class="mt-24"><PTswiper :images="SetNumber(item.card_extend_detail)"></PTswiper></view>
                  <!-- <swiper
                  :autoplay="rowCardSwiperConfig.autoplay"
                  :interval="rowCardSwiperConfig.interval"
                  :circular="rowCardSwiperConfig.circular"
                  :indicator-dots="rowCardSwiperConfig.indicatorDots"
                  :disable-touch="rowCardSwiperConfig.disableTouch"
                >
                  <swiper-item
                    v-for="(swiperItem, swiperIndex) in SetNumber(item.card_extend_detail)"
                    :key="swiperIndex"
                  >
                    <view class="d-flex j-sb a-center mt-24">
                      <view
                        v-for="(inItem, inIndex) in swiperItem.list"
                        :key="inIndex"
                        class="d-flex flex-column j-center"
                        @click="
                          jump.appAndMiniJumpBD(
                            1,
                            `${routeTable.pgGoodsDetail}?id=${inItem.id}`,
                            2,
                            2,
                            105000,
                            inItem.id,
                            from
                          )
                        "
                      >
                        <view
                          class="w-140 h-140 d-flex j-center a-center b-rad-06 o-hid"
                          :class="inIndex == 0 ? 'bg-li-23' : 'bg-li-24'"
                        >
                          <vh-image
                            :loading-type="4"
                            :src="inItem.product_img"
                            :width="120"
                            :height="120"
                            :border-radius="6"
                          />
                        </view>
                        <view class="w-140 font-18 text-9 text-hidden-1">{{ inItem.title }}</view>
                        <view class="l-h-40">
                          <text
                            v-if="inItem.is_hidden_price || [3, 4].includes(inItem.onsale_status)"
                            class="font-32 font-wei text-e80404"
                            >价格保密</text
                          >
                          <template v-else>
                            <text class="font-32 font-wei text-e80404"
                              ><text class="font-18">¥</text>{{ inItem.price }}</text
                            >
                          </template>
                        </view>
                      </view>
                    </view>
                  </swiper-item>
                </swiper> -->
                </view>

                <!-- 直播 -->
                <view
                  v-if="item.style == 7 && item.card_extend_detail.length"
                  class="bg-ffffff w-346 h-308 b-rad-10 o-hid mt-14 pl-24 pr-24"
                >
                  <view class="d-flex a-center mt-20">
                    <view class="">
                      <vh-image
                        v-if="item.card_class == 2"
                        :loading-type="2"
                        :src="item.card_name"
                        :width="126"
                        :height="32"
                        mode="aspectFit"
                      />
                      <view v-else class="font-32 font-wei text-3 l-h-32">{{ item.card_name }}</view>
                    </view>
                    <view class="p-rela w-152 h-34 ml-08">
                      <image class="p-abso w-152 h-34 ml-08" :src="item.sub_title_back" />
                      <view class="p-rela z-02 w-100 h-34 d-flex j-center a-center ml-54 font-24 text-e80404 w-s-now">{{
                        item.sub_title
                      }}</view>
                    </view>
                  </view>

                  <swiper
                    :autoplay="rowCardSwiperConfig.autoplay"
                    :interval="rowCardSwiperConfig.interval"
                    :circular="rowCardSwiperConfig.circular"
                    :indicator-dots="rowCardSwiperConfig.indicatorDots"
                    :disable-touch="rowCardSwiperConfig.disableTouch"
                  >
                    <swiper-item v-for="(inItem, inIndex) in item.card_extend_detail" :key="inIndex">
                      <view
                        class="p-rela bg-f7f7f7 mt-24"
                        @click="jump.jumpAppLiveBD(inItem, 2, 3, 105000, inItem.id, from)"
                      >
                        <image
                          class="p-abso top-06 z-01 left-n-10 w-86 h-34"
                          :src="`${osip}/index/li_bro_ico.png`"
                          mode="aspectFill"
                        />
                        <view class="b-rad-10 o-hid">
                          <vh-image :loading-type="4" :src="inItem.cover_url" :width="298" :height="148" />
                          <view class="mtb-16-mlr-12 font-20 text-3 text-hidden-1">{{ inItem.title }}的活动价位</view>
                        </view>
                      </view>
                    </swiper-item>
                  </swiper>
                </view>
              </block>
            </view>
          </view>

          <!-- 横向滑动的卡片列表 -->
          <view v-if="horizontalScrollCardList.length" class="">
            <view class="" v-for="(item, index) in horizontalScrollCardList" :key="index">
              <!-- 爆款推荐（无缝横版滑动） -->
              <view v-if="item.style == 0 && item.card_extend_detail.length > 0" class="d-flex j-center a-center mt-20">
                <view class="p-rela w-700 h-390">
                  <image class="p-abso w-700 h-390" :src="`${osip}/index/pop_rec_bg.png`" mode="aspectFill" />
                  <view class="p-rela z-02 d-flex j-sb a-center mt-22 ml-24 mr-24">
                    <view class="d-flex a-end">
                      <text class="font-32 font-wei text-be0000 l-h-44">{{ item.card_name }}</text>
                      <text class="ml-14 font-24 text-be0000 l-h-34">{{ item.sub_title }}</text>
                    </view>
                    <view
                      @click="
                        jump.appAndMiniJumpBD(
                          2,
                          `${routeTable.pAMoreThanWine}?id=${item.id}`,
                          2,
                          3,
                          106000,
                          item.id,
                          from
                        )
                      "
                    >
                      <text class="font-24 text-be0000">更多</text>
                      <u-icon name="arrow-right" :size="24" color="#be0000" />
                    </view>
                  </view>
                  <view class="o-aut">
                    <view
                      class="p-rela z-02 w-max-cont d-flex a-center mt-38 ml-nth-child1-24 mr-last-nth-child1-24 o-scr-x"
                    >
                      <view
                        class="w-262 ml-20"
                        v-for="(inItem, inIndex) in item.card_extend_detail"
                        :key="inIndex"
                        @click="
                          jump.appAndMiniJumpBD(
                            1,
                            `${routeTable.pgGoodsDetail}?id=${inItem.id}`,
                            2,
                            2,
                            107000,
                            inItem.id,
                            from
                          )
                        "
                      >
                        <vh-image
                          :loading-type="4"
                          :src="inItem.banner_img"
                          :width="262"
                          :height="162"
                          :border-radius="6"
                        />
                        <view class="mt-10 font-24 text-3 l-h-34 text-hidden-1">{{ inItem.title }}</view>
                        <view class="p-rela w-262 h-42 mt-10">
                          <image class="p-abso w-262 h-42" :src="`${osip}/index/pop_rec_price.png`" mode="aspectFill" />
                          <view class="p-rela w-262 h-42 d-flex j-sb a-center pl-12 pr-12">
                            <text
                              v-if="inItem.is_hidden_price || [3, 4].includes(inItem.onsale_status)"
                              class="font-18 font-wei text-ffffff"
                              >价格保密</text
                            >
                            <text v-else class="font-wei text-ffffff"
                              ><text class="font-18">¥</text>{{ inItem.price }}</text
                            >
                            <text class="ml-06 font-22 text-9">抢</text>
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 易引口粮（有缝横版滑动） -->
              <view v-if="item.style == 1 && item.card_extend_detail.length > 0" class="d-flex j-center a-center mt-20">
                <view class="p-rela w-702 h-374">
                  <image
                    class="p-abso w-702 h-374"
                    src="https://images.vinehoo.com/vinehoomini/v3/index/easy_dri_bg.png"
                    mode="aspectFill"
                  />
                  <view class="p-rela z-02 d-flex j-sb a-center mt-22 ml-24 mr-20">
                    <view class="d-flex a-end">
                      <text class="font-32 font-wei text-153e68 l-h-44">{{ item.card_name }}</text>
                      <text class="ml-12 font-24 text-153e68 l-h-34">{{ item.sub_title }}</text>
                    </view>
                    <view
                      @click="
                        jump.appAndMiniJumpBD(
                          2,
                          `${routeTable.pAMoreThanWine}?id=${item.id}`,
                          2,
                          3,
                          106000,
                          item.id,
                          from
                        )
                      "
                    >
                      <text class="font-24 text-153e68">更多</text>
                      <u-icon name="arrow-right" :size="24" color="#153e68"></u-icon>
                    </view>
                  </view>

                  <view class="o-aut">
                    <view
                      class="p-rela z-02 w-max-cont d-flex a-center mt-20 ml-nth-child1-24 mr-last-nth-child1-24 o-scr-x"
                    >
                      <view
                        class="ml-10"
                        v-for="(inItem, inIndex) in item.card_extend_detail"
                        :key="inIndex"
                        @click="
                          jump.appAndMiniJumpBD(
                            1,
                            `${routeTable.pgGoodsDetail}?id=${inItem.id}`,
                            2,
                            2,
                            107000,
                            inItem.id,
                            from
                          )
                        "
                      >
                        <view class="bg-ffffff b-rad-04 pt-12 pr-12 pb-14 pl-12">
                          <vh-image
                            :loading-type="4"
                            :src="inItem.banner_img"
                            :width="252"
                            :height="156"
                            :border-radius="6"
                          />
                          <view class="">
                            <view class="w-252 mt-08 font-24 text-3 l-h-34 text-hidden-1">{{ inItem.title }}</view>
                            <view class="">
                              <text
                                v-if="inItem.is_hidden_price || [3, 4].includes(inItem.onsale_status)"
                                class="font-32 font-wei text-e80404"
                                >价格保密</text
                              >
                              <template v-else>
                                <text class="font-32 font-wei text-e80404"
                                  ><text class="font-18">¥</text>{{ inItem.price }}</text
                                >
                                <!-- <text class="ml-06 font-20 text-9 text-dec-l-t">¥{{ inItem.market_price }}</text> -->
                              </template>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>

              <!--果蔬（竖版滑动） -->
              <view v-if="item.style == 2 && item.card_extend_detail.length > 0" class="d-flex j-center a-center mt-20">
                <view class="p-rela w-702 h-406">
                  <image
                    class="p-abso w-702 h-406"
                    src="https://images.vinehoo.com/vinehoomini/v3/index/fr_veg_bg.png"
                    mode="aspectFill"
                  />
                  <view class="p-rela z-02 d-flex j-sb a-center mt-22 ml-24 mr-20">
                    <view class="d-flex a-end">
                      <text class="font-32 font-wei text-824729 l-h-44">{{ item.card_name }}</text>
                      <text class="ml-12 font-24 text-824729 l-h-34">{{ item.sub_title }}</text>
                    </view>
                    <view
                      @click="
                        jump.appAndMiniJumpBD(
                          2,
                          `${routeTable.pAMoreThanWine}?id=${item.id}`,
                          2,
                          3,
                          106000,
                          item.id,
                          from
                        )
                      "
                    >
                      <text class="font-24 text-824729">更多</text>
                      <u-icon name="arrow-right" :size="24" color="#824729"></u-icon>
                    </view>
                  </view>
                  <view class="o-aut">
                    <view
                      class="p-rela z-02 w-max-cont d-flex a-center mt-20 ml-nth-child1-24 mr-last-nth-child1-24 o-scr-x"
                    >
                      <view
                        class="ml-10"
                        v-for="(inItem, inIndex) in item.card_extend_detail"
                        :key="inIndex"
                        @click="
                          jump.appAndMiniJumpBD(
                            1,
                            `${routeTable.pgGoodsDetail}?id=${inItem.id}`,
                            2,
                            2,
                            107000,
                            inItem.id,
                            from
                          )
                        "
                      >
                        <view class="d-flex flex-column j-center bg-ffffff b-rad-04 o-hid p-10">
                          <vh-image
                            :loading-type="4"
                            :src="inItem.product_img"
                            :width="180"
                            :height="180"
                            :border-radius="4"
                          />
                          <view class="pt-10 pb-12">
                            <view class="text-center font-20 text-3 text-hidden-1">{{ inItem.title }}</view>
                            <view class="d-flex j-center a-end">
                              <text
                                v-if="inItem.is_hidden_price || [3, 4].includes(inItem.onsale_status)"
                                class="font-32 font-wei text-e80404"
                                >价格保密</text
                              >
                              <template v-else>
                                <text class="font-32 font-wei text-e80404"
                                  ><text class="font-18">¥</text>{{ inItem.price }}</text
                                >
                              </template>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 跨境尾货（白图滑动）-->
              <view v-if="item.style == 3 && item.card_extend_detail.length > 0" class="d-flex j-center a-center mt-20">
                <view class="p-rela w-702 h-288">
                  <image
                    class="p-abso w-702 h-288"
                    src="https://images.vinehoo.com/vinehoomini/v3/index/tail_bg.png"
                    mode="aspectFill"
                  />
                  <view class="p-rela z-02 d-flex j-sb a-center mt-28 ml-24 mr-24">
                    <view class="d-flex a-end">
                      <text class="font-32 font-wei text-422684 l-h-44">{{ item.card_name }}</text>
                      <text class="ml-12 font-24 text-422684 l-h-34">{{ item.sub_title }}</text>
                    </view>
                    <view
                      @click="
                        jump.appAndMiniJumpBD(
                          2,
                          `${routeTable.pAMoreThanWine}?id=${item.id}`,
                          2,
                          3,
                          106000,
                          item.id,
                          from
                        )
                      "
                    >
                      <text class="font-24 text-422684">更多</text>
                      <u-icon name="arrow-right" :size="24" color="#422684" />
                    </view>
                  </view>
                  <view class="o-aut">
                    <view
                      class="p-rela z-02 w-max-cont d-flex a-center mt-24 ml-nth-child1-24 mr-last-nth-child1-24 o-scr-x"
                    >
                      <view
                        class="ml-10"
                        v-for="(inItem, inIndex) in item.card_extend_detail"
                        :key="inIndex"
                        @click="
                          jump.appAndMiniJumpBD(
                            1,
                            `${routeTable.pgGoodsDetail}?id=${inItem.id}`,
                            2,
                            2,
                            107000,
                            inItem.id,
                            from
                          )
                        "
                      >
                        <view class="p-rela w-346 h-166 d-flex j-center a-center b-rad-10 o-hid p-12">
                          <image
                            class="p-abso w-346 h-166"
                            src="https://images.vinehoo.com/vinehoomini/v3/index/tail_goods_bg.png"
                            mode="aspectFill"
                          />
                          <view class="p-rela z-02 bg-f6f4ff">
                            <vh-image
                              :loading-type="4"
                              :src="inItem.product_img"
                              :width="128"
                              :height="142"
                              :border-radius="4"
                            />
                          </view>
                          <view class="p-rela z-02 h-p100 d-flex flex-column j-sb ml-12">
                            <view class="w-184 font-22 text-3 text-hidden-3">{{ inItem.title }}</view>
                            <!-- <view>
                                                          <text class="bg-fde6c9 b-rad-04 ptb-02-plr-08 mt-12 font-18 text-b46f3a">跨境名庄-尾货</text>
                                                      </view> -->
                            <view class="">
                              <text
                                v-if="inItem.is_hidden_price || [3, 4].includes(inItem.onsale_status)"
                                class="font-32 font-wei text-e80404"
                                >价格保密</text
                              >
                              <template v-else>
                                <text class="font-32 font-wei text-e80404"
                                  ><text class="font-18">¥</text>{{ inItem.price }}</text
                                >
                                <!-- <text class="ml-06 font-20 text-9 text-dec-l-t">¥{{ inItem.market_price }}</text> -->
                              </template>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 自然酒 -->
              <view v-if="false" class="d-flex j-center a-center mt-20">
                <view class="p-rela w-702 h-374">
                  <image
                    class="p-abso w-702 h-374"
                    src="https://images.vinehoo.com/vinehoomini/v3/index/nat_wine_bg.png"
                    mode="aspectFill"
                  />
                  <view class="p-rela z-02 d-flex j-sb a-center mt-22 ml-24 mr-20">
                    <view class="d-flex a-center">
                      <text class="font-32 font-wei text-103d23 l-h-44">自然酒</text>
                      <text class="ml-12 font-24 text-103d23">独一无二 纯正风味</text>
                    </view>
                    <view class="">
                      <text class="font-24 text-103d23">更多</text>
                      <u-icon name="arrow-right" :size="24" color="#103d23" />
                    </view>
                  </view>
                  <view class="p-rela z-02 d-flex a-center mt-20 ml-14 mr-20 o-scr-x">
                    <view class="ml-10" v-for="(item1, index1) in 5" :key="index1">
                      <view class="bg-ffffff b-rad-04 pt-12 pr-12 pb-22 pl-12">
                        <view class="w-252 b-rad-04 o-hid">
                          <vh-image :loading-type="4" :src="item.banner_img" :height="156" />
                        </view>
                        <view class="">
                          <view class="w-252 mt-08 font-24 text-3 l-h-34 o-hid text-hidden-1"
                            >Roberto Morra Baro is my asas…</view
                          >
                          <view class="">
                            <text class="font-32 font-wei text-e80404"><text class="font-18">¥</text>158</text>
                            <text class="ml-06 font-20 text-9 text-dec-l-t">¥279</text>
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- tabs选项栏 -->
        <view class="tabs p-rela z-01 mt-32 grayscale-100">
          <!-- 选项栏 -->
          <view
            id="tabs-content"
            class="tran-2 p-stic z-980 h-100 d-flex j-sa a-center bb-s-01-f5f5f5 pl-24 pr-24"
            :class="{ 'choose-wine-fixed': isChooseWineFixed }"
            :style="{ top: `${tabsTop}px`, background: tabsBackground }"
          >
            <block v-for="(item, index) in tabsList" :key="index">
              <view
                v-if="index > 0"
                class="p-rela w-01"
                :class="isFixedTabs ? 'h-44' : 'h-56'"
                :style="{ background: tabsDevideBackground }"
              />
              <view class="p-rela h-p100 d-flex flex-column a-center j-center" @click="changeTabs(index)">
                <view class="p-rela h-44">
                  <image
                    v-if="item.icon"
                    :src="ossIcon(item.icon[currentTabs == index ? 'hpath' : 'path'])"
                    :style="item.icon.style"
                  />
                  <view
                    v-else
                    class="font-32 font-wei l-h-44"
                    :class="currentTabs == index ? 'text-e80404' : 'text-3'"
                    >{{ item.name }}</view
                  >
                </view>
                <view
                  v-show="!isFixedTabs"
                  class="fade-in flex-c-c w-104 h-32 mt-06 font-22 b-rad-16"
                  :class="currentTabs == index ? 'text-ffffff bg-li-1' : 'text-9'"
                >
                  <view style="font-size: 22px; white-space: nowrap; transform: scale(0.5)">{{ item.intro }}</view>
                </view>
                <view v-show="isFixedTabs" class="p-abso w-p100 bottom-06 d-flex j-center a-center">
                  <view class="fade-in w-36 h-08 b-rad-04" :class="currentTabs == index ? 'bg-li-12' : ''" />
                </view>
              </view>
            </block>
          </view>

          <!-- 酒闻、新品、酒会、直播列表 -->
          <view id="tabs-content-list" :style="{ minHeight: `${tabsContentMinHeight}px` }">
            <view class="'mb-20'">
              <!-- 酒闻列表 -->
              <view v-if="currentTabs == 1">
                <view
                  class="p-rela z-01 bg-ffffff mt-20 mr-24 ml-24 p-20 b-rad-10 d-flex j-sb"
                  v-for="(item, index) in wineSmellList"
                  :key="index"
                  @click="jump.appAndMiniJump(1, `${routeTable.pDWineSmellDetail}?id=${item.id}`, from)"
                >
                  <vh-image :loading-type="4" :src="item.img" :width="188" :height="188" :border-radius="10" />
                  <view class="ml-20 flex-1 d-flex flex-column j-sb">
                    <view class="">
                      <view class="font-28 text-3 l-h-36 text-hidden-2">{{ item.title }}</view>
                      <view class="mt-12 font-24 text-9 l-h-34 o-hid text-hidden-1">{{ item.abst }}</view>
                    </view>

                    <view class="mt-36 d-flex j-end">
                      <view class="d-flex a-center mr-56">
                        <image class="w-26 h-26" :src="`${osip}/comm/view.png`" mode="widthFix" />
                        <text class="ml-06 font-24 text-9 l-h-34">{{ item.viewnums | numToThousands }}</text>
                      </view>
                      <view class="d-flex a-center">
                        <image class="w-26 h-26" :src="`${osip}/comm/comm.png`" mode="widthFix" />
                        <text class="ml-06 font-24 text-9 l-h-34">{{ item.commentnums | numToThousands }}</text>
                      </view>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 新品列表 -->
              <view v-if="currentTabs == 0" class="p-rela z-01 mt-20 ml-24 mr-24">
                <view v-if="newProductList.length">
                  <view
                    class="bg-ffffff b-rad-10 mb-20 o-hid t-trans-3d-1"
                    v-for="(item, index) in newProductList"
                    :key="index"
                  >
                    <!-- 广告位（酒会） -->
                    <vh-normal-wine-party-ad v-if="item.modul == 1" :from="from" :item="item" />
                    <!-- 广告位（直播） -->
                    <vh-normal-live-ad v-else-if="item.modul == 2" :from="from" :item="item" />
                    <!-- 广告位（专题活动） -->
                    <vh-normal-thematic-activities-ad v-else-if="item.modul == 3" :from="from" :item="item" />
                    <!-- 广告位（专题产品） -->
                    <vh-normal-special-products-ad v-else-if="item.modul == 4" :from="from" :item="item" />
                    <!-- 常规商品 -->
                    <vh-normal-goods-list
                      :id="'goods_' + index"
                      :newYearTheme="newYearTheme"
                      v-else
                      :from="from"
                      :item="item"
                    />
                  </view>
                </view>
              </view>

              <!-- 酒会列表 -->
              <view v-if="currentTabs == 2">
                <view
                  class="p-rela z-01 mt-20 mr-24 ml-24 b-rad-10 o-hid"
                  v-for="(item, index) in winePartyList"
                  :key="index"
                  @click="jump.appAndMiniJump(1, `${routeTable.pDWinePartyDetail}?id=${item.id}`, from)"
                >
                  <vh-image :src="item.thumb_image" :height="356" />
                  <view class="bg-ffffff p-24">
                    <view class="">
                      <!-- <text v-if="item.source == 1" class="bg-li-1 ptb-02-plr-08 b-rad-06 mr-12 font-26 text-ffffff l-h-30">官方</text> -->
                      <text class="font-28 font-wei text-3 l-h-40">{{ item.title }}</text>
                    </view>

                    <view class="d-flex a-center mt-20">
                      <image class="w-30 h-30" :src="`${osip}/comm/tim_red.png`" mode="aspectFill"></image>
                      <text class="ml-10 text-6 font-24">{{ item.activity_time }}</text>
                    </view>

                    <view class="d-flex a-center mt-16">
                      <image class="w-30 h-30" :src="`${osip}/comm/add_red.png`" mode="aspectFill"></image>
                      <view class="flex-1 ml-10 text-6 font-24 text-hidden-1"
                        >{{ item.province_name }}{{ item.city_name }}{{ item.district_name }}{{ item.address }}</view
                      >
                    </view>

                    <view class="d-flex j-sb a-center mt-20">
                      <view v-if="item.money == '0.00'" class="font-36 font-wei text-e80404 l-h-50">免费</view>
                      <view v-else class="l-h-50">
                        <text class="font-44 font-wei text-e80404"
                          ><text class="mr-06 font-24">¥</text>{{ item.money }}</text
                        >
                        <text class="ml-06 font-28 text-9">起</text>
                      </view>

                      <view class="">
                        <view
                          v-if="item.status == 0"
                          class="w-150 h-42 b-rad-22 bg-e80404 d-flex j-center a-center font-24 font-wei text-ffffff"
                          >立即报名</view
                        >
                        <view v-if="false" class="font-28 font-wei text-3">报名人数已满</view>
                        <view v-if="false" class="">
                          <u-button
                            shape="circle"
                            :hair-line="false"
                            :ripple="true"
                            ripple-bg-color="#FFF"
                            :custom-style="{
                              width: '150rpx',
                              height: '42rpx',
                              fontSize: '24rpx',
                              fontWeight: '500',
                              color: '#666',
                              backgroundColor: '#FFF',
                              border: '1rpx solid #979797',
                            }"
                            >已报名</u-button
                          >
                        </view>
                        <view v-if="item.status == 1" class="">
                          <u-button
                            shape="circle"
                            :hair-line="false"
                            :ripple="true"
                            ripple-bg-color="#FFF"
                            :custom-style="{
                              width: '150rpx',
                              height: '42rpx',
                              fontSize: '24rpx',
                              fontWeight: '500',
                              color: '#FFF',
                              backgroundColor: '#DDDDDD',
                              border: 'none',
                            }"
                            @click="feedback.toast({ title: '报名已结束', icon: 'error' })"
                            >已结束</u-button
                          >
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 直播列表 -->
              <view v-if="currentTabs == 3">
                <view class="ptb-00-plr-24 pt-20">
                  <!-- <ChooseWineGoodsList :list="chooseWineList" /> -->
                  <LiquorGoodsList
                    :list="liquorActivityGoodsList"
                    :page="page"
                    :totalPage="totalPage"
                    @loadMore="handleScrollToLower"
                    isWhiteTheme
                  ></LiquorGoodsList>
                </view>
                <!-- <view class="p-rela z-01 mt-20 mr-24 ml-24 b-rad-20 o-hid" v-for="(item, index) in liveBroadcastList" :key="index" @click="openAppLive(item, 0)">
                              <view class="p-rela w-702 h-350">
                                  <view v-if="item.status == 1" class="p-abso w-280 h-60">
                                      <image class="p-abso z-02 top-0 left-0 w-280 h-60" :src="`${osip}/index/li_bro_bla.png`" mode="aspectFill" />
                                      <view class="p-abso z-03 top-0 left-0 w-280 h-60 d-flex a-center">
                                          <image class="w-26 h-28 ml-24" :src="`${osip}/index/li_bro_app_ico.png`" mode="aspectFill"></image>
                                          <text class="ml-06 font-24 text-ffffff">{{item.subscribe_count}}人已预约</text>
                                      </view>
                                  </view>
                                  
                                  <view v-if="item.status == 2" class="p-abso w-280 h-60">
                                      <image class="p-abso z-02 top-0 left-0 w-280 h-60" :src="`${osip}/index/li_bro_red.png`" mode="aspectFill" />
                                      <view class="p-abso z-03 top-0 left-0 w-280 h-60 d-flex a-center">
                                          <image class="w-26 h-28 ml-24" :src="`${osip}/index/li_bro_ing_ico.png`" mode="aspectFill"></image>
                                          <text class="ml-06 font-24 text-ffffff">正在直播</text>
                                      </view>
                                  </view>
                                  
                                  <view v-if="item.status == 3" class="p-abso w-280 h-60">
                                      <image class="p-abso z-02 top-0 left-0 w-280 h-60" :src="`${osip}/index/li_bro_bla.png`" mode="aspectFill" />
                                      <view class="p-abso z-03 top-0 left-0 w-280 h-60 d-flex a-center">
                                          <image class="w-26 h-28 ml-24" :src="`${osip}/index/li_bro_com_ico.png`" mode="aspectFill"></image>
                                          <text class="ml-06 font-24 text-ffffff">直播已结束</text>
                                      </view>
                                  </view>
                                  
                                  <vh-image :src="item.cover_url" :height="350"/>
                              </view>
                              
                              <view class="bg-ffffff p-24">
                                  <view class="font-28 text-3 l-h-40">{{item.title}}</view>
                                  
                                  <view class="d-flex j-sb a-center mt-20">
                                      <view v-if="item.status < 3" class="d-flex a-center">
                                          <image class="w-30 h-30" :src="`${osip}/comm/tim_red.png`" mode="aspectFill" />
                                          <text class="ml-06 font-24 text-6 l-h-40">{{item.start_time}}</text>
                                      </view>
                                      
                                      <view v-if="item.status == 3" class="d-flex a-center">
                                          <view class="d-flex a-center">
                                              <image class="w-30 h-22" :src="`${osip}/comm/view.png`" mode="aspectFill" />
                                              <text class="ml-06 font-24 text-757575">{{item.num}}</text>
                                          </view>
                                          
                                          <view class="d-flex a-center ml-44">
                                              <image class="w-26 h-26" :src="`${osip}/comm/comm.png`" mode="aspectFill" />
                                              <text class="ml-06 font-24 text-757575">{{item.comment}}</text>
                                          </view>
                                      </view>
                                      
                                      <view class="">
                                          <view v-if="item.status == 1 && item.is_subscribe == 0">
                                              <u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
                                              :custom-style="{width:'150rpx', height:'50rpx', fontSize:'28rpx' ,fontWeight:'500', color:'#FFF', backgroundImage: 'linear-gradient(214deg, #5AC2FF 0%, #505CFB 100%)', border:'none'}"
                                              @click="openAppLive(item, 1)">预约直播</u-button>
                                          </view>
                                          
                                          <view v-if="item.status == 1 && item.is_subscribe == 1">
                                              <u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
                                              :custom-style="{width:'150rpx', height:'50rpx', fontSize:'28rpx' ,fontWeight:'500', color:'#333', backgroundColor: '#FFF', border:'1rpx solid #E7E7E7'}"
                                              @click="openAppLive(item, 1)">已预约</u-button>
                                          </view>
                                          
                                          <view v-if="item.status == 2">
                                              <u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
                                              :custom-style="{width:'150rpx', height:'50rpx', fontSize:'28rpx' ,fontWeight:'500', color:'#FFF', backgroundImage: 'linear-gradient(214deg, #FF6161 0%, #E70000 100%)', border:'none'}"
                                              @click="openAppLive(item, 1)">进入直播</u-button>
                                          </view>
                                          
                                          
                                          <view v-if="item.status == 3">
                                              <u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
                                              :custom-style="{width:'150rpx', height:'50rpx', fontSize:'28rpx' ,fontWeight:'500', color:'#FFF', backgroundImage: 'linear-gradient(214deg, #FF9144 0%, #F35D00 100%)', border:'none'}"
                                              @click="openAppLive(item, 1)">回看直播</u-button>
                                          </view>
                                      </view>
                                  </view>
                              </view>
                          </view> -->
              </view>
            </view>

            <!-- 加载更多 -->
            <view v-if="currentTabs != 3" :class="from ? 'pb-40' : 'pb-24'">
              <u-loadmore :status="loadStatus" />
            </view>
          </view>
        </view>

        <!-- 弹框 -->
        <view class="">
          <!-- 新人专属弹窗 -->
          <u-mask :show="showNewPeoMask" @click="showNewPeoMask = false">
            <view class="h-p100 d-flex j-center a-center">
              <!-- 未登录状态 -->
              <view
                v-if="false"
                class="p-rela w-562 h-618 d-flex flex-column j-center a-center"
                @click.stop="showNewPeoMask = true"
              >
                <image class="w-p100 h-p100" :src="`${osip}/index/new_peo_unlog.png`" mode="widthFix" />
                <image
                  class="p-abso top-0 right-22 w-32 h-32"
                  :src="`${osip}/index/del_whi.png`"
                  mode="aspectFill"
                  @click.stop="showNewPeoMask = false"
                />

                <view class="p-abso top-70 d-flex j-center font-28 font-wei text-d22628">- 新人专享礼包 -</view>

                <view class="p-abso top-110">
                  <text class="bg-li-4 bg-cl-txt text-transp font-36 font-wei">¥</text>
                  <text class="bg-li-4 bg-cl-txt text-transp ml-10 font-90 font-wei">400</text>
                </view>

                <view class="p-abso top-296 font-22 text-a54504">专享超值礼包，每人限领一次</view>

                <view class="p-abso bottom-110 w-p100 d-flex j-center">
                  <view class="w-326 h-72" @click.stop=""></view>
                </view>
              </view>

              <!-- 已登录状态 -->
              <view class="p-rela w-562 h-618 d-flex flex-column j-center a-center" @click.stop="showNewPeoMask = true">
                <image class="w-p100 h-p100" :src="`${osip}/index/new_peo_loged.png`" mode="widthFix" />
                <image
                  class="p-abso top-0 right-22 w-32 h-32"
                  :src="`${osip}/index/del_whi.png`"
                  mode="aspectFill"
                  @click.stop="showNewPeoMask = false"
                />

                <view class="p-abso top-76 d-flex j-center font-24 text-a54504">您的新人礼包将过期</view>

                <view class="p-abso top-120">
                  <text class="font-26 font-wei text-a54504">距失效</text>
                  <text class="ml-04 font-36 font-wei text-d22628">6天23:59:47</text>
                </view>

                <view class="p-abso top-208 w-p100 d-flex j-center">
                  <view class="p-rela w-310 h-112">
                    <image
                      class="p-abso w-p100 h-p100"
                      :src="`${osip}/index/new_peo_cou.png`"
                      mode="aspectFill"
                    ></image>

                    <view class="p-rela w-p100 h-p100 d-flex j-sb a-center pl-46 pr-14">
                      <view class="font-64 font-wei text-fbf1ea">400</view>
                      <view class="w-90 text-center font-22 text-fbf1ea">新人专享礼包</view>
                    </view>
                  </view>
                </view>

                <view class="p-abso bottom-110 w-p100 d-flex j-center">
                  <view class="w-326 h-72" @click.stop=""></view>
                </view>
              </view>
            </view>
          </u-mask>
          <NewPeopleFloatingFrame
            v-if="newPeopleFloatingFrameVisible && from == ''"
            :isLogin="userIsLogin"
            :received="newPeopleCouponPackage.collect_status"
            @jumpActivity="onJumpActivityPage"
          />
          <StartupPageOptionsPopup v-if="indexStartupPageCount === 2 && userIsLogin" :type="1" />
        </view>

        <!-- 悬浮按钮 -->
        <view
          :animation="animationData"
          v-if="isShowHoverButton && hoverButtonInfo.show === '1'"
          class="p-fixed z-999 bottom-240 right-24 grayscale-100"
        >
          <view
            class="p-rela w-200 h-210"
            :style="{ width: hoverButtonImageWidth + 6 + 'rpx', height: hoverButtonImageHeight + 28 + 'rpx' }"
          >
            <image
              v-if="isShowHoverDeleteButton"
              class="p-abso z-04 top-0 right-0 w-28 h-28"
              src="https://images.vinehoo.com/vinehoomini/v2/live_broadcast/del.png"
              mode="aspectFill"
              @click="isShowHoverButton = false"
            />
            <view class="p-abso bottom-0 left-0" @click="hoverButtonJump(hoverButtonInfo.path)">
              <vh-image
                :src="hoverButtonInfo.params.icon"
                :width="hoverButtonImageWidth"
                :height="hoverButtonImageHeight"
              />
            </view>
            <!-- <image class="p-abso bottom-0 left-0 w-200 h-200" :style="{width: hoverButtonImageWidth + 'rpx', height: hoverButtonImageHeight + 'rpx'}" :src="hoverButtonInfo.params.icon" mode="aspectFill" @click="hoverButtonJump(hoverButtonInfo.path)" /> -->
          </view>
        </view>
      </view>

      <!-- 加载状态 -->
      <view v-else :style="{ marginTop: from == '' ? 0 : parseInt(appStatusBarHeight) + 46 + 'px' }">
        <vh-skeleton :type="5" bg-color="#FFF" :has-tab-bar="true" />
      </view>

      <!-- 底部导航栏 tabBar -->
      <vh-tabbar
        :loading="loading"
        v-if="from == ''"
        id="index-tabbar"
        tabbarClass="grayscale-100"
        @topRefresh="topRefresh"
        @recordTabbarPages="recordPages"
      />
    </scroll-view>
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import { CHOOSE_WINE_ACTIVITY_ID, LIQUOR_ACTIVITY_ID, USER_RETURNING_URL } from '@/common/js/fun/constant'
import newPeopleMixin from '@/common/js/mixins/newPeopleMixin'
import userMixin from '@/common/js/mixins/userMixin'
import topRefreshMixin from '@/common/js/mixins/topRefreshMixin'
import startupPageOptionsMixin from '@/common/js/mixins/startupPageOptionsMixin'
import longpressCopyMixin from '@/common/js/mixins/longpressCopyMixin'
import PTswiper from '../../components/ImageCarousel'
export default {
  name: 'index',
  mixins: [newPeopleMixin, userMixin, topRefreshMixin, startupPageOptionsMixin, longpressCopyMixin],
  components: {
    PTswiper,
  },
  data() {
    return {
      osip: 'https://images.vinehoo.com/vinehoomini/v3', //oss静态图片地址前缀（oss static image prefix）
      allObj: {}, //所有数据
      loading: true, //加载状态 true = 加载中、false = 结束加载
      from: '', //从哪个端进入 1 = 安卓、2 = ios"
      appStatusBarHeight: '', //状态栏高度
      navBackgroundColor: 'rgba(224, 20, 31, 1)', // 初始化为不透明
      shoppingCartNum: 0, //购物车数量
      unReadTotalNum: 0, //未读消息总数量
      visibleGoods: [],
      swiperList: [], //轮播列表
      briefList: [
        { imgType: 'mall', name: '官方商城' },
        { imgType: 'rig', name: '正品保障' },
        { imgType: 'aft', name: '售后无忧' },
        { imgType: 'glo', name: '全球甄选' },
      ], //酒云网（官方商城、正品保障...）
      goldAreaList: [], //金刚区列表
      bulletinList: [], //通知列表
      capsuleList: [], //胶囊列表
      newPeopleCapsuleList: [], //新人胶囊列表
      activityList: [], //活动列表
      tileCardList: [], //平铺卡片列表
      seckillOpenSaleTimer: null, //秒杀开始售卖定时器
      seckillOpenSaleSeconds: 0, //秒杀距离开始售卖的时间（秒）
      horizontalScrollCardList: [], //横向滑动的卡片列表
      tabsList: [
        { name: '新品', intro: '美酒佳酿', btnId: 2 },
        { name: '酒闻', intro: '最新实事', btnId: 1 },
        { name: '酒会', intro: '品鉴大会', btnId: 3 },
        {
          icon: {
            path: '/index/liquor_76_34.png',
            hpath: '/index/liquor_76_34.png',
            style: { position: 'relative', top: '50%', transform: 'translateY(-50%)', width: '76rpx', height: '34rpx' },
          },
          intro: '醉有应得',
          btnId: 5,
        },
      ], // tabList（默认）
      newYearTheme: false,
      currentTabs: 0, //当前选中的tabs
      wineSmellList: [], //酒闻列表
      newProductList: [], //新品列表
      winePartyList: [], //酒会列表
      liveBroadcastList: [], //直播列表
      chooseWineList: [],
      adList: [], //广告列表
      tabsScrollTop: 1000, //tabs容器离页面的高度
      showNewPeoMask: false, //是否展示新人弹窗

      hoverButtonInfo: {}, //悬浮按钮数据信息
      isShowHoverButton: true, //是否显示悬浮按钮
      isShowHoverDeleteButton: true, //是否显示悬浮按钮的删除按钮
      hoverButtonImageWidth: 0, //悬浮按钮图片宽度
      hoverButtonImageHeight: 0, //悬浮按钮图片高度
      animationData: {}, //动画对象

      page: 1, //第几页
      limit: 10, //每页显示多少条
      totalPage: 1, //总页数
      loadStatus: 'nomore', //加载状态

      rowCardSwiperConfig: {
        autoplay: true,
        interval: 3 * 1000,
        circular: true,
        indicatorDots: false,
        disableTouch: false,
      },
      seckillSwiperInterval: 3 * 1000,
      saleSwiperInterval: 5 * 1000,
      isFixedTabs: false,
      tabsContentMinHeight: 0,
      isJumpUserReturning: false,
      activityPrefix: '',
      liquorActivityLabelList: [],
      scrollTop: 0,
    }
  },

  onLoad(options) {
    this.secondConfig()
    if (!this.dev) {
      this.activityPrefix = 'https://activity-cdn.vinehoo.com'
    } else {
      this.activityPrefix = 'https://test-activity.wineyun.com'
    }
    this.$u.api.flashChannelList().then((res) => {
      const { list = [] } = res?.data || {}
      this.liquorActivityLabelList = []
      list.find((item) => {
        if (item.activity_id === LIQUOR_ACTIVITY_ID()) {
          this.liquorActivityLabelList = item.activity_label
        }
      })
    })
    console.log('-----------------------我是首页的options')
    console.log(options)
    this.system.setNavigationBarBlack()
    if (options.from && options.version) {
      this.from = options.from
      this.muFrom(this.from) //保存来自哪个状态（解决用户在安卓、ios端登录失效的问题）
      // this.muVersion(options.version) //记录app版本号
      window.indexTopRefresh = () => {
        this.topRefresh()
      }
    }
    this.appStatusBarHeight = options.statusBarHeight
    this.initOnLoad(options)

    // 确保导航栏初始背景色正确
    this.navBackgroundColor = 'rgba(224, 20, 31, 1)'
  },

  onShow() {
    this.login
      .isLoginV3(this.$vhFrom, 0)
      .then((isLogin) => {
        this.userIsLogin = isLogin
        // if( isLogin && !this.$vhFrom ) {
        // 	this.recordPages()
        // }
        if (isLogin && !this.isJumpUserReturning) {
          this.$u.api.getUserReturningInfo().then((res) => {
            this.isJumpUserReturning = true
            const {
              data: { is_jump = '0' },
            } = res
            if (is_jump === '1') {
              this.jump.h5Jump(USER_RETURNING_URL(), this.$vhFrom, 4)
            }
          })
        }
      })
      .finally(() => {
        this.initOnShow()
      })
  },

  computed: {
    //Vuex 辅助state函数
    ...mapState(['routeTable', 'agreementPrefix', 'dev']),
    ...mapState('startupPageOptions', ['indexStartupPageCount']),

    // 获取状态栏高度
    navigationBarHeight() {
      return this.system.navigationBarHeight()
    },

    tabsTop({ $app, $appStatusBarHeight }) {
      if ($app) return $appStatusBarHeight + 46
      return 55
    },
    isChooseWineFixed({ currentTabs, isFixedTabs }) {
      return currentTabs === 3 && isFixedTabs
    },
    navbarBackground({ navBackgroundColor, isChooseWineFixed }) {
      // if (isChooseWineFixed) return '#C9C7F9'
      return navBackgroundColor
    },
    tabsBackground({ isChooseWineFixed, isFixedTabs }) {
      // if (isChooseWineFixed) return 'linear-gradient(180deg, #C9C7F9 0%, rgba(205,211,243,0.73) 100%)'
      return isFixedTabs ? '#ffffff' : '#f5f5f5'
    },
    tabsDevideBackground({ isChooseWineFixed, isFixedTabs }) {
      // if (isChooseWineFixed) return 'rgba(248, 248, 248, 0.8)'
      return isFixedTabs ? '#f5f5f5' : '#e0e1e0'
    },
    navbarIconObj({ isChooseWineFixed }) {
      if (isChooseWineFixed)
        return {
          logo: '/logo1.png',
          search: '/flash_purchase/black_ser.png',
          shopping: '/flash_purchase/black_cart.png',
          notice: '/flash_purchase/black_not.png',
        }
      return {
        logo: '/logo1.png',
        search: '/index/white_ser.png',
        shopping: '/index/white_cart.png',
        notice: '/index/white_not.png',
      }
    },
    liquorActivityGoodsList({ chooseWineList, liquorActivityLabelList }) {
      return chooseWineList.map((item) => {
        const { label_name = '', label_color = '' } =
          liquorActivityLabelList.find(({ id }) => id === (item.column_id ? item.column_id[0] : '')) || {}
        return {
          ...item,
          $activityLabelText: label_name,
          $activityLableTextBg: label_color
            ? `linear-gradient(180deg, ${label_color[0]} 0%, ${label_color[1] || label_color[0]} 100%)`
            : '',
        }
      })
    },
  },
  mounted() {
    setInterval(() => {
      const existingData = uni.getStorageSync('visibleGoods') || []
      let newData = [...existingData, ...this.visibleGoods]
      uni.setStorageSync('visibleGoods', newData)
      this.visibleGoods = []
    }, 3000)
  },
  methods: {
    // Vuex mapMutations辅助函数
    ...mapMutations(['muFrom', 'muVersion']),
    handleGoodsScroll(e) {
      // 处理导航栏背景色
      const scrollTop = e.detail.scrollTop
      if (scrollTop <= 100) {
        this.navBackgroundColor = `rgba(224, 20, 31, ${scrollTop / 100})`
      } else {
        this.navBackgroundColor = `rgba(224, 20, 31, 1)`
        this.monitorScroll()
      }

      // 原有的曝光检测逻辑
      this.$nextTick(() => {
        const query = uni.createSelectorQuery().in(this)

        // 处理普通商品列表
        if (!this.currentTabs) {
          // 非酒类标签
          this.newProductList.forEach((item, index) => {
            query
              .select(`#goods_${index}`)
              .boundingClientRect((data) => {
                if (data && data.top <= window.innerHeight && data.bottom >= 0) {
                  let uid = ''
                  let device = ''
                  const userinfo = uni.getStorageSync('loginInfo') || '{}'
                  if (userinfo && userinfo.uid) {
                    uid = userinfo.uid
                  } else {
                    uid = uni.getStorageSync('uniqueId')
                  }
                  if (this.$vhFrom == 'next') {
                    device = 'hm'
                  } else if (this.$vhFrom == '1') {
                    device = 'android'
                  } else if (this.$vhFrom == '2') {
                    device = 'ios'
                  } else {
                    device = 'h5'
                  }
                  const goodsItem = {
                    uid: String(uid),
                    created_time: new Date().getTime(),
                    metric_name: 'period_exposure',
                    device,
                    period: Number(item.id),
                    period_type: Number(item.periods_type),
                  }

                  // 基于ID去重
                  const exists = this.visibleGoods.some((v) => v.period === item.id)
                  if (!exists) {
                    this.visibleGoods.push(goodsItem)
                  }
                }
              })
              .exec()
          })
        }
      })
    },
    async secondConfig() {
      const res = await this.$u.api.secondConfig()

      this.newYearTheme = res.data.isopen
    },
    // onLoad初始化数据（接口聚合:首页聚合、获取悬浮框信息） options = 页面参数
    async initOnLoad(options = {}) {
      await Promise.all([this.getIndexCombine()])
      this.getParams(options)
      this.getSeckillCardData()
    },

    // 初始化onShow（接口聚合:消息通知、购物车数量...）
    async initOnShow() {
      try {
        await Promise.all([
          this.getShoppingCartNum(),
          this.getMessageUnreadNum(),
          this.getHoverButtonInfo(),
          // this.getNewPeopleMarketingAd()
        ])
      } catch (e) {
        //TODO handle the exception
      }
    },
    SetNumber(list) {
      let result = []
      list.map((item) => {
        item.list.map((child) => {
          result.push(child)
        })
      })
      return result
      //   console.log(result)
      //   if (list.length) {
      //     const length = list.length
      //     let lastItem = list[list.length - 1].list
      //     console.warn(lastItem)

      //     const lastItemLength = lastItem.length
      //     console.warn(lastItemLength)
      //     if (lastItemLength === 1) {
      //       const data = lastItem.push(list[0].list[0])
      //       console.log(data)
      //     }
      //   }
      //   return list
    },
    // 获取购物车数量
    async getShoppingCartNum() {
      if (this.login.isLogin(this.from, 0)) {
        let res = await this.$u.api.shoppingCartNum()
        console.log('-----------------------------我是购物车数量接口')
        console.log(res)
        this.shoppingCartNum = res.data
        if (this.$android) {
          const params = {
            fromAppType: '8',
            count: res?.data || 0,
          }
          wineYunJsBridge.setDataFromApp(params)
        }
      } else {
        this.shoppingCartNum = 0
        uni.stopPullDownRefresh()
      }
    },

    // 获取未读消息数据
    async getMessageUnreadNum() {
      if (this.login.isLogin(this.from, 0)) {
        try {
          let res = await this.$u.api.messageUnreadNum()
          this.unReadTotalNum = res.data.total_num
          if (this.$android) {
            const params = {
              fromAppType: '6',
              data: res?.data || {},
            }
            wineYunJsBridge.setDataFromApp(params)
          }
        } catch (e) {
          //TODO handle the exception
        }
      } else {
        this.unReadTotalNum = 0
        uni.stopPullDownRefresh()
      }
    },

    // 获取首页聚合接口
    async getIndexCombine() {
      try {
        this.showTabBar()
        let data = {} //需要上传的数据
        // h5
        if (this.from == '1') data.client = 1 // 1:安卓
        else if (this.from == '2') data.client = 0 // 0:ios
        else if (this.from == 'next') data.client = 5 // 5:鸿蒙
        else data.client = 3 // 3:h5
        let colum = await this.$u.api.IndexClientlist({ channel: 0, client: data.client })
        // 微信小程序
        // data.client = 2 //客户端参数 客户端 0:ios 1:安卓 2:小程序 3:h5
        let res = await this.$u.api.indexCombine(data)
        if (res.data) {
          let { banner, goldArea, bulletin, capsule, activity, card, goods } = res.data // 首页聚合接口所有的数据
          this.allObj = res.data // 所有数据
          this.swiperList = banner.list // 轮播列表（banner）
          this.goldAreaList = goldArea.list // 获取金刚区列表
          this.bulletinList = bulletin.list // 快报列表
          if (capsule.list && capsule.list.length) this.capsuleList = capsule.list // 胶囊列表
          let activityList = [...activity.list]
          colum.data.list.map((item) => {
            item.is_colum = true
            // item.path = this.activityPrefix + item.path
            activityList.push(item)
          })
          if (activityList && activityList.length)
            this.activityList = activityList.filter((v, i) => {
              return i < 4
            }) // 活动列表
          if (card.list && card.list.length)
            this.tileCardList = card.list.filter((v) => {
              return v.pattern == 1 && v?.card_extend_detail?.length
            }) //平铺卡片列表
          const findGroup = this.tileCardList.find((item) => item.style === 6)
          if (findGroup?.card_extend_detail?.length) {
            const len = Math.ceil(findGroup?.card_extend_detail?.length / 2)
            const custom_card_extend_detail = []
            for (let i = 0; i < len; i++) {
              custom_card_extend_detail.push({ list: findGroup.card_extend_detail.slice(i * 2, i * 2 + 2) })
            }
            findGroup.card_extend_detail = custom_card_extend_detail
          }
          if (this.tileCardList.length === 3) this.tileCardList = this.tileCardList.slice(0, 2)
          else if (this.tileCardList.length === 1) this.tileCardList = []
          if (card.list && card.list.length)
            this.horizontalScrollCardList = card.list.filter((v) => {
              return v.pattern == 0
            }) //横向滑动的卡片列表
          // this.wineSmellList = news?.list || [] // 酒闻列表
          // this.totalPage = Math.ceil((news?.total || 0) / this.limit) //总页数
          this.newProductList = goods?.list || []
          this.totalPage = Math.ceil((goods?.total || 0) / this.limit)
          this.loadStatus = this.page == this.totalPage || this.totalPage == 0 ? 'nomore' : 'loadmore' //底部分页加载状态
          this.loading = false //loading弹框
          this.navBackgroundColor = `rgba(224, 20, 31, 0)` //设置导航栏背景
          uni.stopPullDownRefresh() //停止下拉刷新
        } else {
          console.log('--------------------我还没有数据')
          this.loading = false //loading弹框
          uni.stopPullDownRefresh()
        }
      } catch (e) {
        //TODO handle the exception
        console.log('--------------------我进入额首页聚合接口异常')
        console.log(e)
      }
    },

    // 获取新人胶囊广告
    async getNewPeopleMarketingAd() {
      if (this.login.isLogin(this.from, 0)) {
        try {
          let data = {
            type: 7, //类型 类型 7 胶囊新人广告
            channel: 0, //频道 0 首页 1 闪购 2 秒发3 社区 4 兔头 5个人中心 6商家秒发
          }
          if (this.from == '1') data.client = 1 // 1:安卓
          else if (this.from == '2') data.client = 0 // 0:ios
          else if (this.from == 'next') data.client = 5 // 0:ios
          else data.client = 3 // 3:h5
          const {
            data: { list },
          } = await this.$u.api.newPeopleMarketingAd(data)
          this.newPeopleCapsuleList = list
        } catch (e) {
          //TODO handle the exception
        }
      } else {
        this.newPeopleCapsuleList = []
      }
    },

    // 悬浮框按钮
    async getHoverButtonInfo() {
      try {
        let from = ''
        if (this.from == '1') from = 'android'
        else if (this.from == '2') from = 'ios'
        else if (this.from == 'next') from = 'harmonyos'
        else from = 'miniapp'
        let res = await this.$u.api.hoverButtonInfo({ from })
        this.hoverButtonInfo = res.data
        this.hoverButtonImageWidth = parseInt(this.hoverButtonInfo.params.w) * 2 //悬浮按钮宽
        this.hoverButtonImageHeight = parseInt(this.hoverButtonInfo.params.h) * 2 //悬浮按钮高
        console.log(res)
      } catch (e) {
        //TODO handle the exception
      }
    },

    // 获取秒杀卡片
    getSeckillCardData() {
      if (!this.tileCardList.length) return
      console.log('我是获取秒杀卡片数据')
      console.log(this.tileCardList)
      let seckillCardList = this.tileCardList.filter((v) => {
        return v.style == 5 && v.card_extend_detail.length
      })
      if (seckillCardList.length) {
        console.log(seckillCardList[0].card_extend_detail[0].onsale_time)
        this.startCountDown(this.date.getSeconds(seckillCardList[0].card_extend_detail[0].onsale_time))
      }
    },

    // 开始倒计时 timesDiff = 两者时差（秒）
    startCountDown(timesDiff) {
      this.clearTimer()
      if (timesDiff <= 0) return
      this.seckillOpenSaleSeconds = Number(timesDiff)
      this.seckillOpenSaleTimer = setInterval(() => {
        this.seckillOpenSaleSeconds--
        if (this.seckillOpenSaleSeconds <= 0) {
          this.clearTimer()
        }
      }, 1000)
    },

    // 清空定时器
    clearTimer() {
      if (this.seckillOpenSaleTimer) {
        clearInterval(this.seckillOpenSaleTimer)
        this.seckillOpenSaleTimer = null
        this.seckillOpenSaleSeconds = 0
      }
    },

    // 获取酒闻列表
    async getwineSmellList() {
      try {
        let res = await this.$u.api.wineSmellList({ page: this.page, limit: this.limit })
        this.handlePage('wineSmellList', res.data.list, res.data.total)
      } catch (e) {
        //TODO handle the exception
      }
    },

    // 获取新品列表
    async getNewProductList() {
      try {
        let res = await this.$u.api.flashGoodsList({
          page: this.page,
          limit: this.limit,
          sort_type: 'onsale_time',
          order: 'desc',
        })
        // if(this.page == 1) await this.getAdList() //获取广告位数据
        this.handlePage('newProductList', res.data.list, res.data.total)
      } catch (e) {
        //TODO handle the exception
      }
    },

    // 获取广告位数据
    async getAdList() {
      try {
        let res = await this.$u.api.advertisingList({ type: 5, channel: 0, client: 2 }) //获取广告数据
        this.adList = res.data.list.filter((v) => {
          return v.modul_data != null
        }) //过滤不合法数据
        this.adList = this.adList.sort((a, b) => {
          return a.sort - b.sort
        }) //数组排序
      } catch (e) {
        //TODO handle the exception
      }
    },

    // 插入广告位 res = 广告位数据
    insertAd() {
      if (this.currentTabs == 1 && this.adList.length) {
        this.adList.forEach((item, index) => {
          if (
            (this.page - 1) * this.limit <= item.sort &&
            item.sort < this.page * this.limit &&
            item.sort < this.newProductList.length
          ) {
            // sort在列表数据之间
            console.log(
              '-----------------------sort >= 0 && sort < 当前页 * 每页限制数 && sort < newProductList.length'
            )
            this.newProductList.splice(item.sort, 0, item)
          } else if (this.page == this.totalPage && item.sort >= this.newProductList.length) {
            // sort > 总条数
            console.log('-----------------------sort > 总条数')
            this.newProductList.splice(this.newProductList.length, 0, item)
          } else {
            console.log('有数据没插入')
          }
        })
      }
    },

    // 获取酒会列表
    async getWinePartList() {
      try {
        let res = await this.$u.api.winePartyList({ page: this.page, limit: this.limit })
        this.handlePage('winePartyList', res.data.list, res.data.total)
      } catch (e) {
        //TODO handle the exception
      }
    },

    // 获取直播列表
    async getLiveBroadcastList() {
      try {
        let res = await this.$u.api.ossLiveBroadcastList({ page: this.page, limit: this.limit, is_anchor: 0 })
        this.handlePage('liveBroadcastList', res.data.list, res.data.total)
      } catch (e) {
        //TODO handle the exception
      }
    },

    async getChooseWineList() {
      const res = await this.$u.api.flashGoodsList({
        page: this.page,
        limit: this.limit,
        activity_list: [LIQUOR_ACTIVITY_ID()],
        sort_type: 'sort',
        order: 'desc',
      })
      const { list = [], total = 0 } = res.data
      this.handlePage('chooseWineList', list, total)
    },

    // 监听悬浮按钮滚动
    monitorHoverButtomScroll() {
      this.isShowHoverDeleteButton = false
      var animation = uni.createAnimation({ duration: 500, timingFunction: 'linear' })
      this.animation = animation
      animation
        .rotate(-30)
        .right(-(this.hoverButtonImageWidth / 4))
        .opacity(0.7)
        .step()
      this.animationData = animation.export()
      this.$u.debounce(() => {
        this.isShowHoverDeleteButton = true
        animation.rotate(0).right(12).opacity(1).step()
        this.animationData = animation.export()
      }, 2000)
    },

    // 悬浮框跳转 jumpType = 跳转类型 xcx = 小程序、url = 跳转h5、wxkf = 微信客服
    hoverButtonJump(jumpType) {
      const { room_id } = this.hoverButtonInfo //悬浮框信息
      const { url, page, id } = this.hoverButtonInfo.params //悬浮框按钮参数信息
      if (this.comes.isFromApp(this.from)) {
        //App
        switch (jumpType) {
          case 'xcx': //小程序页面
            wineYunJsBridge.openAppPage({
              client_path: { ios_path: 'wx.xcx', android_path: 'wx.xcx' },
              ad_path_param: [
                {
                  ios_key: 'id',
                  ios_val: id,
                  android_key: 'id',
                  android_val: id,
                },
                {
                  ios_key: 'path',
                  ios_val: page,
                  android_key: 'path',
                  android_val: page,
                },
              ],
            })
            break
          case 'xcxzb': //小程序直播
            wineYunJsBridge.openAppPage({
              client_path: { ios_path: 'wx.xcx', android_path: 'wx.xcx' },
              ad_path_param: [
                {
                  ios_key: 'id',
                  ios_val: id,
                  android_key: 'id',
                  android_val: id,
                },
                {
                  ios_key: 'path',
                  ios_val: `${this.routeTable.pgIndex}?room_id=${room_id}`,
                  android_key: 'path',
                  android_val: `${this.routeTable.pgIndex}?room_id=${room_id}`,
                },
              ],
            })
            break
          case 'url': //跳转h5
            console.log('--------我是app跳转h5页面')
            this.jump.h5Jump(url, this.from)
            // wineYunJsBridge.openAppPage({
            // 	client_path: { "ios_path":"GeneralWebViewController", "android_path":"com.stg.rouge.webview.WebActivity" },
            // 	ad_path_param: [
            // 		{
            // 			"ios_key":"url", "ios_val": h5Path,
            // 		    "android_key":"url", "android_val": h5Path
            // 		},
            // 		{
            // 			"ios_key":"isHideNav", "ios_val": "no",//是否交给网页端控制标题栏
            // 			"android_key":"NullKey", "android_val": ""
            // 		},
            // 		{
            // 			"ios_key":"isStatusBar", "ios_val": "no",//是否交给网页端控制状态栏
            // 			"android_key":"NullKey", "android_val": ""
            // 		}
            // 	]
            // })
            break
          case 'wxkf': //微信客服
            console.log('--------我是跳转app微信客服')
            wineYunJsBridge.openAppPage({
              client_path: { ios_path: 'wechatServer', android_path: 'wechatServer' },
            })
            break
        }
      } else {
        //h5或者小程序
        switch (jumpType) {
          // case 'xcx': //小程序页面
          // 	console.log('--------我是跳转小程序页面')
          // 	this.jump.navigateTo(page)
          // break
          // case 'xcxzb': //小程序直播
          // 	console.log('--------我是跳转直播间')
          // 	this.jump.navigateTo(`${this.routeTable.pgMiniLiveRoom}?room_id=${room_id}`)
          // break
          case 'url': //跳转h5
            console.log('--------我是跳转h5页面')
            this.jump.h5Jump(url, this.from, 4)
            // uni.getStorage({
            // 	key: 'loginInfo',
            // 	success: res => {
            // 		let h5Url = this.hoverButtonInfo.params.url
            // 		console.log('已登录')
            // 		if(this.hoverButtonInfo.params.url.indexOf('NewZone') > -1) { //新人活动
            // 			let url = `${h5Url}&token=${res.data}`
            // 			uni.navigateTo({
            // 				url: "../../pagesA/pages/webview/webview?shareParams=isNewZone&path=" + encodeURIComponent(url)
            // 			})
            // 		}else{ //其它
            // 			uni.navigateTo({
            // 				url: "../../pagesA/pages/webview/webview?path=" + encodeURIComponent(h5Url)
            // 			})
            // 		}
            // 	},
            // 	fail: err => {
            // 		console.log('未登录')
            // 		let h5Url = this.hoverButtonInfo.params.url
            // 		if(this.hoverButtonInfo.params.url.indexOf('NewZone') > -1) { //新人活动
            // 			uni.navigateTo({
            // 				url: "../../pagesA/pages/webview/webview?shareParams=isNewZone&path=" + encodeURIComponent(h5Url)
            // 			})
            // 		}else { //其它
            // 			uni.navigateTo({
            // 				url: "../../pagesA/pages/webview/webview?path=" + encodeURIComponent(h5Url)
            // 			})
            // 		}
            // 	}
            // })
            break
          // case 'wxkf': //跳转微信客服
          // 	console.log('--------我是跳转微信客服')
          // 	this.system.customerService()
          // break
          default:
            this.feedback.toast({ title: '请打开小程序或app使用' })
            break
        }
      }
    },

    // 接受h5、安卓、ios通用、参数 options = 页面接受参数
    getParams(options) {
      if (Object.keys(options).length) {
        if (options.room_id) {
          // 包含直播间id（h5、安卓、ios通用）首页当成中转页面，让app跳转到直播间
          this.jump.navigateTo(`${this.routeTable.pgMiniLiveRoom}?room_id=${options.room_id}`)
        }
        // else if(options.distribution_uid){ //包含分销用户id
        // 	uni.setStorageSync('distributionUid', options.distribution_uid)
        // }else if(options.tripartite_source) { //注册标识
        // 	uni.setStorageSync('tripartiteSource', options.tripartite_source)
        // }else if(options.nftPage) { //nft页面
        // 	this.jumpNftPage(options.nftPage)
        // }
      }
    },

    // 处理分页信息 listName = 列表名称（wineSmellList = 酒闻、newProductList = 新品、winePartyList = 酒会、liveBroadcastList = 直播）、resList = 接口返回的列表数据、total = 总条数
    handlePage(listName, resList, total) {
      this.page == 1 ? (this[listName] = resList) : (this[listName] = [...this[listName], ...resList])

      // 确保total是数字
      total = parseInt(total) || 0
      this.limit = parseInt(this.limit) || 10

      // 计算总页数并确保是整数
      this.totalPage = Math.ceil(total / this.limit)

      console.log('处理分页:', listName, '总条数:', total, '每页限制:', this.limit, '总页数:', this.totalPage)

      this.loadStatus = this.page >= this.totalPage || this.totalPage == 0 ? 'nomore' : 'loadmore'
    },

    // 获取tabs数据列表 index = tabs索引
    async getTabsDataList(index) {
      this.feedback.loading()
      switch (index) {
        case 1:
          await this.getwineSmellList()
          break
        case 0:
          await this.getNewProductList()
          break
        case 2:
          await this.getWinePartList()
          break
        case 3:
          // await this.getLiveBroadcastList()
          await this.getChooseWineList()
          break
      }
    },
    JumpPage(item) {
      if (item.is_colum) {
        if (!item.page_mode) {
          let path = ''
          if (!this.dev) {
            path = 'https://activity-cdn.vinehoo.com'
          } else {
            path = 'https://test-activity.wineyun.com'
          }
          this.jump.h5Jump(path + item.path, this.$vhFrom, 4)
        } else {
          if (item.id == 48) {
            this.handleJump(`${this.routeTable.pgMiaoFaCardTemporarily}?cid=${item.id}&type=colum`, this.$vhFrom)
          } else {
            this.handleJump(`${this.routeTable.pgMiaoFaCard}?cid=${item.id}&type=colum`, this.$vhFrom)
          }
        }
      } else {
        this.jump.h5JumpBD(item.activity_url, 2, 3, 104000, item.id, this.$vhFrom, 4)
      }
    },
    // tabs通知swiper切换 index = tabsList索引
    async changeTabs(index) {
      if (!this.tabsContentMinHeight && this.isFixedTabs) {
        const getRect = (selector) => {
          return new Promise((resolve) => {
            uni
              .createSelectorQuery()
              .in(this)
              .select(selector)
              .boundingClientRect((rect) => resolve(rect))
              .exec()
          })
        }
        const reqs = [getRect('#index-navbar'), getRect('#tabs-content')]
        if (!this.from) reqs.push(getRect('#index-tabbar'))
        const [
          { height: navbarHeight } = { height: 0 },
          { height: tabsHeight } = { height: 0 },
          { height: tabbarHeight } = { height: 0 },
        ] = await Promise.all(reqs)
        const { screenHeight } = this.system.getSysInfo()
        this.tabsContentMinHeight = screenHeight - navbarHeight - tabsHeight - tabbarHeight
      }
      const { btnId } = this.tabsList[index]
      this.$u.api.reportBuryDot({
        data: [
          {
            channel: 2,
            genre: 3,
            region_id: 108000,
            button_id: btnId,
          },
        ],
      })
      if (this.currentTabs == index) return
      this.page = 1
      this.totalPage = 1
      await this.getTabsDataList(index)
      this.currentTabs = index
      if (this.isFixedTabs) {
        this.$nextTick(() => {
          this.tabsChangeScroll()
        })
      }
    },

    // tabs切换滚动
    tabsChangeScroll() {
      uni
        .createSelectorQuery()
        .in(this)
        .select('.tabs')
        .boundingClientRect((data) => {
          uni
            .createSelectorQuery()
            .in(this)
            .select('#outer-content')
            .boundingClientRect((res) => {
              // setTimeout(() => { this.system.pageScrollTo( data.top - res.top - 100 , 100 ) }, 500 )
              let scrollTop = data.top - res.top
              if (this.$app) {
                scrollTop = scrollTop - this.tabsTop
              }
              scrollTop++
              this.system.pageScrollTo(scrollTop, 0)
            })
            .exec()
        })
        .exec()
    },

    // 打开全局搜索
    openGlobalSearch() {
      if (this.comes.isFromApp(this.from)) {
        wineYunJsBridge.openAppPage({
          client_path: {
            ios_path: 'GlobalSearchViewController',
            android_path: 'com.stg.rouge.activity.SearchActivity',
          },
          ad_path_param: [{ ios_key: 'channel', ios_val: 'index', android_key: 'from', android_val: '3' }],
        })
      } else {
        this.jump.navigateTo(`${this.routeTable.pFGlobalSearch}?type=0`)
      }
    },

    // 打开购物车
    openShoppingCar() {
      if (this.comes.isFromApp(this.from)) {
        wineYunJsBridge.openAppPage({
          client_path: {
            ios_path: 'ShoppingCartViewController',
            android_path: 'com.stg.rouge.activity.ShopCarActivity',
          },
          ad_path_param: [{ ios_key: 'login', android_key: 'login' }],
        })
      } else {
        this.jump.loginNavigateTo(this.routeTable.pBShoppingCart)
      }
    },

    // 打开消息中心
    openMessageCenter() {
      if (this.comes.isFromApp(this.from)) {
        wineYunJsBridge.openAppPage({
          client_path: {
            ios_path: 'MessageCenterViewController',
            android_path: 'com.stg.rouge.activity.MessageCenterActivity',
          },
          ad_path_param: [{ ios_key: 'login', android_key: 'login' }],
        })
      } else {
        this.jump.loginNavigateTo(this.routeTable.pEMessageCenter)
      }
    },

    // 打开直播 item = 列表某一项, type、clickType = 点击类型 0 = 卡片列表、1 = 按钮
    openAppLive(item, clickType) {
      console.log(item)
      if (this.comes.isFromApp(this.from)) {
        let { id, status, is_subscribe } = item //直播列表某一项
        if (status == 1 && clickType == 0) {
          this.feedback.toast({ title: '直播未开始~' })
        } else {
          if (status == 1) {
            //1：已创建直播
            if (is_subscribe) {
              this.feedback.toast({ title: '您已经预约过该直播~' })
            } else {
              if (this.comes.isFromApp(this.from)) {
                this.feedback.showModal({
                  content: '您确认预约该直播吗？',
                  confirm: async () => {
                    try {
                      await this.$u.api.liveBroadcastReservation({ live_id: id })
                      this.feedback.toast({ title: '预约成功~', icon: 'success' })
                      this.getLiveBroadcastList()
                    } catch (e) {}
                  },
                })
              }
            }
          } else {
            //1:未开播 2：直播中、//3：已结束
            // wineYunJsBridge.openAppPage({
            // 	client_path: { "ios_path":"LiveDetailsViewController", "android_path":"com.stg.rouge.activity.LiveActivity"},
            // 	ad_path_param: [{ "ios_key":"live_id", "ios_val":id, "android_key":"id", "android_val":id }],
            // })
            this.jump.jumpAppLive(item, this.from)
          }
        }
      } else {
        this.feedback.toast({ title: '请前往APP查看此功能~' })
      }
    },

    // 是否显示底部tabBar
    showTabBar() {
      if (this.from) {
        uni.hideTabBar()
      }
    },

    // 监听页面滚动
    monitorScroll() {
      uni
        .createSelectorQuery()
        .in(this)
        .select('.tabs')
        .boundingClientRect((res) => {
          // this.tabsScrollTop = res.top
          this.isFixedTabs = res.top <= this.tabsTop
        })
        .exec()
    },

    // 刷新页面
    refreshPage() {
      this.page = 1
      this.totalPage = 1
      this.initOnLoad()
      this.initOnShow()
    },

    // 新增滚动到底部事件处理函数
    handleScrollToLower() {
      console.log('触发了 scrolltolower 事件', this.page, this.totalPage)

      // 确保page和totalPage是数字类型
      const page = parseInt(this.page) || 1
      const totalPage = parseInt(this.totalPage) || 1

      if (page < totalPage && this.loadStatus !== 'loading') {
        console.log('开始加载更多数据，当前页：', page, '总页数：', totalPage)
        this.loadStatus = 'loading'
        this.page = page + 1
        this.getTabsDataList(this.currentTabs)
      } else {
        console.log(
          '没有更多数据了 或 正在加载中',
          '当前页：',
          page,
          '总页数：',
          totalPage,
          'loadStatus:',
          this.loadStatus
        )
      }
    },
  },

  onUnload() {
    console.log('--------onUnload')
    this.clearTimer()
  },

  beforeDestroy() {
    console.log('--------beforeDestroy')
    // this.clearTimer()
  },

  onPullDownRefresh() {
    this.refreshPage()
  },

  onShareAppMessage(res) {
    return {
      title: '酒云网 与百万发烧友一起淘酒',
      path: this.routeTable.pgIndex,
      imageUrl: this.swiperList[0].image,
    }
  },

  onPageScroll(res) {
    this.monitorHoverButtomScroll() //监听悬浮框滚动
    if (res.scrollTop <= 100) {
      this.navBackgroundColor = `rgba(224, 20, 31, ${res.scrollTop / 100})`
    } else {
      this.monitorScroll()
      this.navBackgroundColor = `rgba(224, 20, 31, 1)`
    }
  },

  onReachBottom() {
    // 恢复原始的 onReachBottom 处理，保证两种方式都能工作
    if (this.page == this.totalPage || this.totalPage == 0) return
    this.loadStatus = 'loading'
    this.page++
    this.getTabsDataList(this.currentTabs)
  },
}
</script>

<style>
@import '../../common/css/page.css';
</style>

<style lang="scss" scoped>
.sale-swiper-item {
  @include iconBg('/index/fri_goods_bg.png', 298rpx 196rpx);
}

// .choose-wine-fixed {
// 	&::before {
// 		content: '';
// 		position: absolute;
// 		top: 0;
// 		left: 0;
// 		@include size(100%);
// 		background: linear-gradient(180deg, #C9C7F9 0%, rgba(205,211,243,0.73) 100%);
// 	}
// }
</style>
