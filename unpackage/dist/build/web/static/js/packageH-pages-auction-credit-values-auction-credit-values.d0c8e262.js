(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageH-pages-auction-credit-values-auction-credit-values"],{"12c6":function(t,e,a){"use strict";a.r(e);var n=a("51bd"),i=a("f074");for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);a("f2f9");var r=a("f0c5"),o=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"519170ec",null,!1,n["a"],void 0);e["default"]=o.exports},3353:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return n}));var n={vhNavbar:a("12c6").default,uTabs:a("b14f").default,AuctionNone:a("f6b7").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[a("vh-navbar",{attrs:{title:"信用值",height:"46",showBorder:!0}},[a("v-uni-view",{staticClass:"flex-c-c p-24 font-30 text-6",attrs:{slot:"right"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.jump.navigateTo(t.routeTable.pHAuctionCreditValuesIntro)}},slot:"right"},[t._v("说明")])],1),t.loading?t._e():a("v-uni-view",[a("v-uni-view",{staticClass:"p-rela mtb-24-mlr-auto w-686 h-240"},[a("v-uni-image",{staticClass:"p-abso top-0 w-p100 h-p100",attrs:{src:t.ossIcon("/auction/cv_card_686_240.png")}}),a("v-uni-view",{staticClass:"p-rela pt-48 h-p100"},[a("v-uni-view",{staticClass:"font-wei-600 font-84 text-ffffff l-h-84 text-center"},[t._v(t._s(t.creditValues))]),a("v-uni-view",{staticClass:"p-abso bottom-46 w-p100 flex-c-c"},[a("v-uni-text",{staticClass:"font-24 text-ffffff l-h-40"},[t._v("我的信用分")]),a("v-uni-image",{staticClass:"ml-06 w-18 h-24",attrs:{src:t.ossIcon("/auction/credit_values_h_18_24.png")}})],1)],1)],1),a("v-uni-view",{staticClass:"auction-cv__tabs flex-c-c mt-16"},[a("u-tabs",{attrs:{list:t.tabsList,current:t.currentTabIndex,height:"64","font-size":"28","active-color":"#333","inactive-color":"#666","bar-width":"36","bar-height":"8",gutter:"62"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onTabChange.apply(void 0,arguments)}}})],1),a("v-uni-view",{staticClass:"h-02 bg-eeeeee"}),t.list.length?a("v-uni-view",{staticClass:"mt-28 ptb-00-plr-48"},t._l(t.list,(function(e,n){return a("v-uni-view",{key:n,staticClass:"flex-sb-c ptb-32-plr-00 bb-s-02-eeeeee"},[a("v-uni-view",[a("v-uni-view",{staticClass:"font-30 text-3 l-h-40"},[t._v(t._s(e.reason))]),a("v-uni-view",{staticClass:"mt-10 font-24 text-9 l-h-40"},[t._v(t._s(e.created_time))])],1),a("v-uni-view",{staticClass:"flex-c-c"},[a("v-uni-text",{staticClass:"font-32",class:e.operation===t.MAuctionCreditValuesOperation.Add?"text-e80404":"text-3"},[t._v(t._s(e.operation===t.MAuctionCreditValuesOperation.Add?"+":"-")+t._s(e.score))]),a("v-uni-image",{staticClass:"ml-06 w-18 h-24",attrs:{src:t.ossIcon("/auction/credit_values"+(e.operation===t.MAuctionCreditValuesOperation.Add?"_h":"")+"_18_24.png")}})],1)],1)})),1):a("AuctionNone",{staticClass:"pt-140",attrs:{img:"/auction/none_442_400.png",imgClazz:"w-442 h-400",desc:"暂无记录～",descClazz:""}})],1)],1)},s=[]},"400f":function(t,e,a){"use strict";a.r(e);var n=a("424b"),i=a.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);e["default"]=i.a},"424b":function(t,e,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d3b7"),a("3ca3"),a("ddb0"),a("99af");var i=n(a("f07e")),s=n(a("c964")),r=n(a("f3f3")),o=a("d8be"),u=a("26cb"),c={name:"auctionCreditValues",data:function(){return{MAuctionCreditValuesOperation:o.MAuctionCreditValuesOperation,loading:!0,tabsList:[{name:"全部",type:o.MAuctionCreditValuesTab.All},{name:"增加记录",type:o.MAuctionCreditValuesTab.Add},{name:"扣分记录",type:o.MAuctionCreditValuesTab.Sub}],currentTabIndex:0,loginInfo:{},creditValues:0,query:{page:1,limit:10},totalPage:0,list:[]}},computed:(0,r.default)({},(0,u.mapState)(["routeTable"])),methods:{load:function(){var t=this;Promise.all([this.loadCreditValues(),this.loadList()]).finally((function(){t.loading=!1}))},loadCreditValues:function(){var t=this;return(0,s.default)((0,i.default)().mark((function e(){var a,n;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.userSpecifiedData({field:"auction_credit_score"});case 2:n=e.sent,t.creditValues=(null===n||void 0===n||null===(a=n.data)||void 0===a?void 0:a.auction_credit_score)||0;case 4:case"end":return e.stop()}}),e)})))()},loadList:function(){var t=this;return(0,s.default)((0,i.default)().mark((function e(){var a,n,s,o,u,c,l;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=t.tabsList[t.currentTabIndex].type,e.next=3,t.$u.api.searchAuctionCreditValuesList((0,r.default)((0,r.default)({},t.query),{},{uid:t.loginInfo.uid,type:a}));case 3:n=e.sent,s=(null===n||void 0===n?void 0:n.data)||{},o=s.list,u=void 0===o?[]:o,c=s.total,l=void 0===c?0:c,t.list=1===t.query.page?u:t.list.concat(u),t.totalPage=Math.ceil(l/t.query.limit);case 7:case"end":return e.stop()}}),e)})))()},onTabChange:function(t){this.currentTabIndex=t,this.query=this.$options.data().query,this.loadList()}},onLoad:function(){var t=this;this.login.isLoginV3(this.$vhFrom).then((function(e){e&&(t.loginInfo=uni.getStorageSync("loginInfo")||{},t.load())}))},onReachBottom:function(){this.query.page!==this.totalPage&&this.totalPage&&(this.query.page++,this.loadList())}};e.default=c},4320:function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.auction-cv__tabs[data-v-d55ccc5e] .u-tab-item{min-width:%?112?%;box-sizing:initial;font-weight:600!important;vertical-align:top}.auction-cv__tabs[data-v-d55ccc5e] .u-tab-bar{bottom:auto;background:linear-gradient(214deg,#ff8383,#e70000)}',""]),t.exports=e},"51bd":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return n}));var n={uIcon:a("e5e1").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{},[a("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":t.isFixed},t.navbarClass],style:[t.navbarStyle]},[a("v-uni-view",{staticClass:"vh-status-bar",style:{height:(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}),a("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":t.newYearTheme},style:[t.navbarInnerStyle]},[t.isBack?a("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[t.vhFrom?a("u-icon",{attrs:{name:"nav-back",color:t.backIconColor,size:t.backIconSize}}):[a("u-icon",{attrs:{name:t.pageLength<=1?"home":t.backIconName,color:t.backIconColor,size:t.backIconSize}}),t.backText?a("v-uni-view",{staticClass:"vh-back-text",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()]],2):t._e(),t.title?a("v-uni-view",{staticClass:"vh-navbar-content-title",style:[t.titleStyle]},[a("v-uni-view",{staticClass:"vh-title",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),a("v-uni-view",{staticClass:"vh-slot-content"},[t._t("default")],2),a("v-uni-view",{staticClass:"vh-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?a("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}):t._e()],1)},s=[]},"7b0c":function(t,e,a){"use strict";a.r(e);var n=a("d72e"),i=a.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);e["default"]=i.a},"7f1a":function(t,e,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a("f3f3"));a("a9e3"),a("caad6"),a("2532");var s=a("26cb"),r=uni.getSystemInfoSync(),o={},u={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:o,statusBarHeight:r.statusBarHeight,appStatusBarHeight:0}},computed:(0,i.default)((0,i.default)({},(0,s.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),this.showBorder&&Object.assign(t,this.borderStyle),t},titleStyle:function(){var t={};return t.left=(r.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(r.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var t=this.pages.getPageFullPath(),e=this.routeTable,a=e.pEAddressAdd,n=e.pEAddressManagement,i=e.pBOrderDepositDetail;(t.includes("/packageH")||t.includes(a)||t.includes(n)||t.includes(i))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};e.default=u},80129:function(t,e,a){var n=a("4320");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("4f06").default;i("35231ae0",n,!0,{sourceMap:!1,shadowMode:!1})},8493:function(t,e,a){"use strict";var n=a("80129"),i=a.n(n);i.a},a126:function(t,e,a){var n=a("bbdc");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("4f06").default;i("703d0287",n,!0,{sourceMap:!1,shadowMode:!1})},b29b:function(t,e,a){"use strict";a.r(e);var n=a("3353"),i=a("400f");for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);a("8493");var r=a("f0c5"),o=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"d55ccc5e",null,!1,n["a"],void 0);e["default"]=o.exports},bbdc:function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),t.exports=e},c8f9:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[a("v-uni-view",{staticClass:"flex-c-c"},[a("v-uni-image",{class:t.imgClazz,attrs:{src:t.ossIcon(t.img)}})],1),t.btnText?a("v-uni-view",{staticClass:"flex-c-c",class:t.btnBlockClazz},[a("v-uni-button",{staticClass:"vh-btn flex-c-c w-208 h-64 font-wei-500 font-32 text-e80404 bg-ffffff b-rad-29 b-s-02-e80404",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.btnFun.apply(void 0,arguments)}}},[t._v(t._s(t.btnText))])],1):t._e(),t.title?a("v-uni-view",{staticClass:"font-wei-500 font-36 text-3 l-h-50 text-center",class:t.titleClazz},[t._v(t._s(t.title))]):t._e(),t.desc?a("v-uni-view",{staticClass:"font-28 text-6 l-h-40 text-center",class:t.descClazz},[t._v(t._s(t.desc))]):t._e()],1)},i=[]},d72e:function(t,e,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a("e143")),s={props:{img:{type:String,default:"/auction/none_440_400.png"},imgClazz:{type:String,default:"w-440 h-400"},btnText:{type:String,default:""},btnFun:{type:Function,default:function(){var t=i.default.prototype,e=t.$app,a=t.$ios;e&&(a?wineYunJsBridge.openAppPage({client_path:"jumpCommunityAuction"}):wineYunJsBridge.openAppPage({client_path:{android_path:"goMain"},ad_path_param:[{android_key:"type",android_val:"2"},{android_key:"str",android_val:"1"}]}))}},btnBlockClazz:{type:String,default:"mt-68"},title:{type:String,default:""},titleClazz:{type:String,default:"mt-72"},desc:{type:String,default:""},descClazz:{type:String,default:"mt-20"}}};e.default=s},f074:function(t,e,a){"use strict";a.r(e);var n=a("7f1a"),i=a.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);e["default"]=i.a},f2f9:function(t,e,a){"use strict";var n=a("a126"),i=a.n(n);i.a},f6b7:function(t,e,a){"use strict";a.r(e);var n=a("c8f9"),i=a("7b0c");for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);var r=a("f0c5"),o=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"79787d9f",null,!1,n["a"],void 0);e["default"]=o.exports}}]);