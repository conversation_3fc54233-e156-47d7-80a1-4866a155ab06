(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageH-pages-auction-my-participation-auction-my-participation","packageB-pages-order-deposit-order-deposit~packageB-pages-order-invoice-history-list-order-invoice-h~beef88ad"],{"0efb":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"vh-image-con",class:[t.isMf?"mf-card":""],style:[t.wrapStyle,t.backgroundStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick.apply(void 0,arguments)}}},[t.isError?t._e():a("v-uni-image",{staticClass:"vh-image",style:{backgroundColor:t.bgColor,borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)},attrs:{src:t.getImage,mode:t.mode,"lazy-load":t.lazyLoad,"show-menu-by-longpress":t.showMenuByLongpress},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.onErrorHandler.apply(void 0,arguments)},load:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoadHandler.apply(void 0,arguments)}}}),t.showLoading&&t.loading?a("v-uni-view",{staticClass:"vh-image-loading",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius),backgroundColor:this.bgColor}},[a("v-uni-image",{style:[t.wrapStyle],attrs:{src:t.getLoadingImage,mode:t.mode}})],1):t._e(),t.showError&&t.isError&&!t.loading?a("v-uni-view",{staticClass:"u-image-error",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)}},[a("v-uni-image",{style:[t.wrapStyle],attrs:{src:t.getLoadingImage,mode:t.mode}})],1):t._e()],1)},i=[]},"12c6":function(t,e,a){"use strict";a.r(e);var n=a("51bd"),i=a("f074");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a("f2f9");var r=a("f0c5"),s=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"519170ec",null,!1,n["a"],void 0);e["default"]=s.exports},"208a":function(t,e,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d3b7"),a("b64b"),a("159b"),a("d81d"),a("99af"),a("13d5"),a("4de4");var i=n(a("f07e")),o=n(a("c964")),r=n(a("f3f3")),s=a("06cd"),u=a("d8be"),c=a("26cb"),l=n(a("c715")),d={name:"auctionMyParticipation",mixins:[l.default],data:function(){return{TabType:u.MAuctionMyParticipationStatus,tabList:[{name:"竞拍中",type:u.MAuctionMyParticipationStatus.Bidding},{name:"已拍中",type:u.MAuctionMyParticipationStatus.Shot},{name:"未拍中",type:u.MAuctionMyParticipationStatus.NotTaken}],isEdit:!1,popupVisible:!1,currentItem:null}},computed:(0,r.default)((0,r.default)({},(0,c.mapState)(["routeTable"])),{},{rightText:function(t){var e=t.isEdit;return e?"完成":"编辑"},checkAll:function(t){var e=t.list;return!!e.length&&Object.keys(e).every((function(t){return e[t].list.every((function(t){return t.$checked}))}))},checkSome:function(t){var e=t.list;return!!e.length&&Object.keys(e).some((function(t){return e[t].list.some((function(t){return t.$checked}))}))}}),methods:{load:function(t,e){var a=this;return(0,o.default)((0,i.default)().mark((function n(){var o,c,l,d,f,p,h;return(0,i.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return o=(0,r.default)({},t),c=a.tabList[e].type,c===u.MAuctionMyParticipationStatus.Bidding?o.onsale_status=2:c===u.MAuctionMyParticipationStatus.Shot?o.is_auction=1:c===u.MAuctionMyParticipationStatus.NotTaken&&(o.is_auction=2),n.next=5,a.$u.api.auctionParticipateList(o);case 5:return l=n.sent,d=(null===l||void 0===l?void 0:l.data)||{},f=d.list,p=void 0===f?[]:f,h=d.total,void 0===h?0:h,Object.keys(p).forEach((function(t){p[t].list=p[t].list.map((function(t){return Object.assign({},s.MAuctionMyParticipationMapper[c],t,{$checked:!1})}))})),a.list=1===t.page?p:a.list.concat(p),a.currentTabIndex=e,n.abrupt("return",l);case 11:case"end":return n.stop()}}),n)})))()},changeTabs:function(t){this.isEdit=!1,this.changeTabIndex(t)},onRightTextClick:function(){this.isEdit=!this.isEdit},onEnjoy:function(t){var e=this;return(0,o.default)((0,i.default)().mark((function a(){var n,o;return(0,i.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(n=t.is_like,o=t.goods_id,!n){a.next=6;break}e.popupVisible=!0,e.currentItem=t,a.next=10;break;case 6:return a.next=8,e.$u.api.addEnjoyAuctionGoods({goods_id:o});case 8:e.feedback.toast(),t.is_like=1;case 10:case"end":return a.stop()}}),a)})))()},onConfirm:function(){var t=this;return(0,o.default)((0,i.default)().mark((function e(){return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.cancelEnjoyAuctionGoods({goods_id:t.currentItem.goods_id});case 2:t.popupVisible=!1,t.currentItem.is_like=0;case 4:case"end":return e.stop()}}),e)})))()},onCheckAllChange:function(){var t=this,e=this.checkAll;Object.keys(this.list).forEach((function(a){t.list[a].list.forEach((function(t){t.$checked=!e}))}))},onBatchCancel:function(){var t=this;this.feedback.showModal({content:"确认删除吗？",confirm:function(){var e=(0,o.default)((0,i.default)().mark((function e(){var a;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(a=Object.keys(t.list).map((function(e){return t.list[e].list.filter((function(t){return t.$checked})).map((function(t){return t.id}))})).reduce((function(t,e){return t.concat(e)}),[]).join(","),!a){e.next=7;break}return e.next=4,t.$u.api.auctionParticipateListDel({id:a});case 4:t.feedback.toast({title:"删除成功",icon:"success"}),t.feedback.loading(),t.load((0,r.default)((0,r.default)({},t.query),{},{page:1})).then((function(){t.$nextTick((function(){t.system.pageScrollTo(0,0)}))}));case 7:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}()})}},onLoad:function(){var t=this;this.login.isLoginV3(this.$vhFrom).then((function(e){e&&t.load().finally((function(){t.loading=!1}))}))},onPullDownRefresh:function(){this.pullDownRefresh()},onReachBottom:function(){this.reachBottomLoad()}};e.default=d},"2da4":function(t,e,a){var n=a("39e4");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("4f06").default;i("64a51bd6",n,!0,{sourceMap:!1,shadowMode:!1})},"34dc":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return n}));var n={vhNavbar:a("12c6").default,uTabs:a("b14f").default,vhImage:a("ce7c").default,uLoadmore:a("776f").default,AuctionNone:a("f6b7").default,uPopup:a("c4b0").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{class:t.list.length?"h-min-vh-100 bg-f5f5f5":""},[a("vh-navbar",{attrs:{title:"我的参拍",height:"46","show-border":!0}},[t.list.length&&t.currentTabIndex!==t.TabType.Bidding?a("v-uni-view",{staticClass:"flex-c-c w-108 h-92 font-30 text-3",attrs:{slot:"right"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onRightTextClick.apply(void 0,arguments)}},slot:"right"},[t._v(t._s(t.rightText))]):t._e()],1),t.loading?t._e():a("v-uni-view",{class:t.isEdit?"pb-128":"pb-24"},[a("v-uni-view",{staticClass:"p-stic z-980",style:{top:t.system.navigationBarHeight()+"px"}},[a("u-tabs",{attrs:{list:t.tabList,current:t.currentTabIndex,height:92,"font-size":32,"inactive-color":"#333","active-color":"#E80404","bar-width":36,"bar-height":8,"bar-style":{background:"linear-gradient(214deg, #FF8383 0%, #E70000 100%)"},"is-scroll":!1},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.changeTabs.apply(void 0,arguments)}}})],1),a("v-uni-view",{staticClass:"o-hid"},[t.list.length?a("v-uni-view",{staticClass:"ptb-00-plr-24",class:t.isEdit?"t-trans-x-72":""},[t._l(t.list,(function(e,n){return a("v-uni-view",{key:n,staticClass:"p-rela bg-ffffff b-rad-10 mtb-20-mlr-00"},[a("v-uni-view",{staticClass:"p-abso top-16 left-20 font-wei-500 font-28 text-6"},[t._v(t._s(e.time))]),t._l(e.list,(function(e,n){return a("v-uni-view",{key:n,staticClass:"p-rela ptb-00-plr-20",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.jump.navigateTo(t.routeTable.pHAuctionGoodsDetail+"?id="+e.goods_id)}}},[a("v-uni-view",{staticClass:"p-abso top-84 left-0 t-trans-x-m100 flex-c-c w-96"},[a("v-uni-image",{staticClass:"w-32 h-32",attrs:{src:t.ossIcon("/auction/radio"+(e.$checked?"_h":"")+"_32.png")},on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),e.$checked=!e.$checked}}})],1),a("v-uni-view",{staticClass:"p-abso top-0 right-0 w-176 h-46"},[a("v-uni-image",{staticClass:"p-abso w-p100 h-p100",attrs:{src:t.ossIcon("/auction_my_participation/status_bg"+([t.TabType.Shot,t.TabType.NotTaken].includes(t.tabList[t.currentTabIndex].type)?e.$statusBgIndex:e[e.is_lead].$statusBgIndex)+".png")}}),a("v-uni-view",{staticClass:"p-rela flex-c-c w-p100 h-p100"},[a("v-uni-image",{staticClass:"w-36 h-34",attrs:{src:t.ossIcon("/auction_my_participation/hammer.png")}}),a("v-uni-text",{staticClass:"ml-10 font-22 l-h-34 text-ffffff"},[t._v(t._s([t.TabType.Shot,t.TabType.NotTaken].includes(t.tabList[t.currentTabIndex].type)?e.$statusText:e[e.is_lead].$statusText))])],1)],1),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:n,expression:"index1"}],staticClass:"h-02 bg-eeeeee"}),a("v-uni-view",{staticClass:"d-flex pt-70 pb-24"},[a("vh-image",{attrs:{"loading-type":4,src:e.product_img&&e.product_img[0],width:152,height:152,"border-radius":6}}),a("v-uni-view",{staticClass:"flex-1 flex-col-sb-s ml-20"},[a("v-uni-view",{},[a("v-uni-view",{staticClass:"font-wei-500 font-24 text-3 l-h-34 text-hidden-2"},[t._v(t._s(e.title))]),a("v-uni-text",{staticClass:"mt-04 ptb-00-plr-08 font-22 l-h-32 b-rad-04",class:[e.$tagBgClazz,e.$tagTextClazz]},[t.currentTabIndex===t.TabType.Bidding?[t._v(t._s(e.closing_auction_time_text)+t._s(e.$tagText))]:[t._v(t._s(e.$tagText))]],2)],1),a("v-uni-view",{staticClass:"w-p100 flex-sb-c"},[a("v-uni-view",{staticClass:"flex-c-c text-e80404"},[a("v-uni-text",{staticClass:"font-24"},[t._v(t._s(e.$priceName))]),a("v-uni-text",{staticClass:"ml-06 font-24"},[a("v-uni-text",[t._v("¥")]),a("v-uni-text",{staticClass:"font-32 font-wei"},[t._v(t._s(e.final_auction_price))])],1)],1),a("v-uni-image",{staticClass:"w-24 h-24",attrs:{src:t.ossIcon("/auction_my_participation/"+(e.is_like?"":"un")+"like.png")},on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.onEnjoy(e)}}})],1)],1)],1)],1)}))],2)})),a("u-loadmore",{attrs:{status:t.reachBottomLoadStatus}})],2):a("AuctionNone",{staticClass:"pt-200",attrs:{title:t.$app?"":"空空如也",desc:"做名副其实的捡漏王～",descClazz:t.$app?"mt-30":"mt-20"}}),t.list.length&&t.isEdit?a("v-uni-view",{directives:[{name:"safeBeautyBottom",rawName:"v-safeBeautyBottom",value:t.$safeBeautyBottom,expression:"$safeBeautyBottom"}],staticClass:"p-fixed left-0 bottom-0 flex-sb-c w-p100 h-104 bg-ffffff b-sh-00021200-022 z-9999"},[a("v-uni-view",{staticClass:"flex-sb-c ml-24",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onCheckAllChange.apply(void 0,arguments)}}},[a("v-uni-image",{staticClass:"ml-08 w-32 h-32",attrs:{src:t.ossIcon("/auction/radio"+(t.checkAll?"_h":"")+"_32.png")}}),a("v-uni-text",{staticClass:"ml-16 font-32 text-3"},[t._v("全选")])],1),a("v-uni-button",{staticClass:"vh-btn flex-c-c mr-24 w-208 h-64 bg-e80404 font-wei-500 font-28 text-ffffff b-rad-32",attrs:{disabled:!t.checkSome},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onBatchCancel.apply(void 0,arguments)}}},[t._v("删除")])],1):t._e()],1)],1),a("u-popup",{attrs:{mode:"center",width:"552rpx",height:"414rpx","border-radius":"20"},model:{value:t.popupVisible,callback:function(e){t.popupVisible=e},expression:"popupVisible"}},[a("v-uni-view",{staticClass:"p-rela w-552 h-414"},[a("v-uni-image",{staticClass:"p-abso w-552 h-414",attrs:{src:t.ossIcon("/auction/popup_bg_552_414.png")}}),a("v-uni-view",{staticClass:"p-rela pt-84"},[a("v-uni-view",{staticClass:"font-wei-500 font-32 text-3 l-h-44 text-center"},[t._v("真的不喜欢我了嘛")]),a("v-uni-view",{staticClass:"mt-20 font-28 text-6 l-h-34 text-center"},[t._v("哼，善变的兔子君！")]),a("v-uni-view",{staticClass:"flex-sb-c mt-84 ptb-00-plr-70"},[a("v-uni-button",{staticClass:"vh-btn flex-c-c w-160 h-64 font-wei-500 font-28 text-6 bg-ffffff b-s-02-666666 b-rad-32",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onConfirm.apply(void 0,arguments)}}},[t._v("确认")]),a("v-uni-button",{staticClass:"vh-btn flex-c-c w-160 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.popupVisible=!1}}},[t._v("取消")])],1)],1)],1)],1)],1)},o=[]},"39e4":function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-load-more-wrap[data-v-15067509]{display:flex;flex-direction:row;justify-content:center;align-items:center}.u-load-more-inner[data-v-15067509]{display:flex;flex-direction:row;justify-content:center;align-items:center;padding:0 %?12?%}.u-more[data-v-15067509]{position:relative;display:flex;flex-direction:row;justify-content:center}.u-dot-text[data-v-15067509]{font-size:%?28?%}.u-loadmore-icon-wrap[data-v-15067509]{margin-right:%?8?%}.u-loadmore-icon[data-v-15067509]{display:flex;flex-direction:row;align-items:center;justify-content:center}',""]),t.exports=e},"3dc3":function(t,e,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a("d0af")),o=n(a("f3f3"));a("a9e3"),a("d3b7"),a("159b"),a("e25e"),a("c975");var r=a("26cb"),s={name:"u-image",props:{loadingType:{type:[String,Number],default:1},isMf:{type:Boolean,default:!1},src:{default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!1},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:100},bgColor:{type:String,default:"transparent"},isResize:{type:Boolean,default:!1},resizeRatio:{type:Object,default:function(){return{wratio:16,hratio:9}}},resizeUsePx:{type:Boolean,default:!1},opacityProp:{type:Number,default:1},backgroundImage:{type:String,default:""}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(t){t?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:(0,o.default)((0,o.default)({},(0,r.mapState)(["ossPrefix"])),{},{wrapStyle:function(){var t={};if(t.width=this.$u.addUnit(this.width),t.height=this.$u.addUnit(this.height),this.backgroundImage&&(t.backgroundImage="url(".concat(this.backgroundImage,")"),t.backgroundSize="100% 100%",t.backgroundRepeat="no-repeat",t.backgroundPosition="center"),this.isResize){var e,a,n=(null===this||void 0===this||null===(e=this.src)||void 0===e||null===(a=e.split("?"))||void 0===a?void 0:a[1])||"",o=n.split("&"),r={};o.forEach((function(t){var e=t.split("="),a=(0,i.default)(e,2),n=a[0],o=a[1];r[n]=o}));var s=+((null===r||void 0===r?void 0:r.w)||""),u=+((null===r||void 0===r?void 0:r.h)||"");if(!isNaN(s)&&!isNaN(u)&&s&&u){var c=parseInt(this.width),l=c/s*u,d=this.resizeRatio,f=d.wratio,p=d.hratio;if("auto"!==f&&"auto"!==p){var h=c*f/p,v=c*p/f;l>h?l=h:l<v&&(l=v)}this.resizeUsePx?t.height="".concat(l,"px"):t.height=this.$u.addUnit(l)}}return t.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),t.overflow=this.borderRadius>0||"circle"==this.shape?"hidden":"visible",this.fade&&(t.opacity=this.opacity,t.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),t},getLoadingImage:function(){return"https://images.vinehoo.com/vinehoomini/v3/occupy_img/new_img".concat(this.loadingType,".png")},getImage:function(){return"string"!==typeof this.src?"":-1==this.src.indexOf("http")?this.ossPrefix+this.src:this.src}}),methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(t){this.loading=!1,this.isError=!0,this.$emit("error",t)},onLoadHandler:function(){var t=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){t.durationTime=t.duration,t.opacity=t.opacityProp,setTimeout((function(){t.removeBgColor()}),t.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};e.default=s},"51bd":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return n}));var n={uIcon:a("e5e1").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{},[a("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":t.isFixed},t.navbarClass],style:[t.navbarStyle]},[a("v-uni-view",{staticClass:"vh-status-bar",style:{height:(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}),a("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":t.newYearTheme},style:[t.navbarInnerStyle]},[t.isBack?a("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[t.vhFrom?a("u-icon",{attrs:{name:"nav-back",color:t.backIconColor,size:t.backIconSize}}):[a("u-icon",{attrs:{name:t.pageLength<=1?"home":t.backIconName,color:t.backIconColor,size:t.backIconSize}}),t.backText?a("v-uni-view",{staticClass:"vh-back-text",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()]],2):t._e(),t.title?a("v-uni-view",{staticClass:"vh-navbar-content-title",style:[t.titleStyle]},[a("v-uni-view",{staticClass:"vh-title",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),a("v-uni-view",{staticClass:"vh-slot-content"},[t._t("default")],2),a("v-uni-view",{staticClass:"vh-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?a("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}):t._e()],1)},o=[]},5402:function(t,e,a){var n=a("7e2d");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("4f06").default;i("78a60126",n,!0,{sourceMap:!1,shadowMode:!1})},"6ab5":function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-image-con[data-v-89d76102]{position:relative;transition:opacity .5s ease-in}.vh-image[data-v-89d76102]{width:100%;height:100%}.vh-image-loading[data-v-89d76102],\n.u-image-error[data-v-89d76102]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;background-color:#fff;color:#909399;font-size:%?46?%}.mf-card[data-v-89d76102]{background-color:#f7f7f7!important;padding:5px}',""]),t.exports=e},"776f":function(t,e,a){"use strict";a.r(e);var n=a("e643"),i=a("e4d5");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a("afb6");var r=a("f0c5"),s=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"15067509",null,!1,n["a"],void 0);e["default"]=s.exports},"7b0c":function(t,e,a){"use strict";a.r(e);var n=a("d72e"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},"7e2d":function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */[data-v-9ac734a8] .u-tab-item{font-weight:600}',""]),t.exports=e},"7f1a":function(t,e,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a("f3f3"));a("a9e3"),a("caad6"),a("2532");var o=a("26cb"),r=uni.getSystemInfoSync(),s={},u={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:s,statusBarHeight:r.statusBarHeight,appStatusBarHeight:0}},computed:(0,i.default)((0,i.default)({},(0,o.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),this.showBorder&&Object.assign(t,this.borderStyle),t},titleStyle:function(){var t={};return t.left=(r.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(r.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var t=this.pages.getPageFullPath(),e=this.routeTable,a=e.pEAddressAdd,n=e.pEAddressManagement,i=e.pBOrderDepositDetail;(t.includes("/packageH")||t.includes(a)||t.includes(n)||t.includes(i))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};e.default=u},"8a04":function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3");var n={name:"u-loadmore",props:{bgColor:{type:String,default:"transparent"},icon:{type:Boolean,default:!0},fontSize:{type:String,default:"28"},color:{type:String,default:"#606266"},status:{type:String,default:"loadmore"},iconType:{type:String,default:"circle"},loadText:{type:Object,default:function(){return{loadmore:"加载更多",loading:"正在加载...",nomore:"没有更多了"}}},isDot:{type:Boolean,default:!1},iconColor:{type:String,default:"#b7b7b7"},marginTop:{type:[String,Number],default:0},marginBottom:{type:[String,Number],default:0},height:{type:[String,Number],default:"auto"}},data:function(){return{dotText:"●"}},computed:{loadTextStyle:function(){return{color:this.color,fontSize:this.fontSize+"rpx",position:"relative",zIndex:1,backgroundColor:this.bgColor}},cricleStyle:function(){return{borderColor:"#e5e5e5 #e5e5e5 #e5e5e5 ".concat(this.circleColor)}},flowerStyle:function(){return{}},showText:function(){var t="";return t="loadmore"==this.status?this.loadText.loadmore:"loading"==this.status?this.loadText.loading:"nomore"==this.status&&this.isDot?this.dotText:this.loadText.nomore,t}},methods:{loadMore:function(){"loadmore"==this.status&&this.$emit("loadmore")}}};e.default=n},a126:function(t,e,a){var n=a("bbdc");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("4f06").default;i("703d0287",n,!0,{sourceMap:!1,shadowMode:!1})},afb6:function(t,e,a){"use strict";var n=a("2da4"),i=a.n(n);i.a},b1db:function(t,e,a){"use strict";a.r(e);var n=a("208a"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},b252:function(t,e,a){var n=a("6ab5");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("4f06").default;i("0c30beb4",n,!0,{sourceMap:!1,shadowMode:!1})},bbdc:function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),t.exports=e},bc18:function(t,e,a){"use strict";var n=a("5402"),i=a.n(n);i.a},c715:function(t,e,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d3b7"),a("159b"),a("b64b");var i=n(a("f07e")),o=n(a("f3f3")),r=n(a("c964")),s={name:"listMixin",data:function(){return{loading:!0,list:[],query:{page:1,limit:10},totalPage:0,currentTabIndex:0,reachBottomLoadStatus:"loading",isLastIdPattern:!1,isListPattern:!1}},methods:{load:function(){},reload:function(){var t=this;return(0,r.default)((0,i.default)().mark((function e(){var a;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=t.isLastIdPattern?(0,o.default)((0,o.default)({},t.query),{},{last_id:0}):(0,o.default)((0,o.default)({},t.query),{},{page:1}),e.next=3,t.load(a);case 3:case"end":return e.stop()}}),e)})))()},reachBottomLoad:function(){var t=this;return(0,r.default)((0,i.default)().mark((function e(){var a;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("loading"!==t.reachBottomLoadStatus){e.next=2;break}return e.abrupt("return");case 2:if(!t.isLastIdPattern){e.next=7;break}if(t.query.last_id){e.next=5;break}return e.abrupt("return");case 5:e.next=14;break;case 7:if(!t.isListPattern){e.next=12;break}if("nomore"!==t.reachBottomLoadStatus){e.next=10;break}return e.abrupt("return");case 10:e.next=14;break;case 12:if(t.query.page!==t.totalPage&&t.totalPage){e.next=14;break}return e.abrupt("return");case 14:return t.reachBottomLoadStatus="loading",a=t.isLastIdPattern?t.query:(0,o.default)((0,o.default)({},t.query),{},{page:t.query.page+1}),e.next=18,t.load(a).catch((function(){t.reachBottomLoadStatus="loadmore"}));case 18:case"end":return e.stop()}}),e)})))()},changeTabIndex:function(t){var e=this;return(0,r.default)((0,i.default)().mark((function a(){var n;return(0,i.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return n=e.isLastIdPattern?(0,o.default)((0,o.default)({},e.query),{},{last_id:0}):(0,o.default)((0,o.default)({},e.query),{},{page:1}),a.next=3,e.load(n,t);case 3:e.onChangeToScroll();case 4:case"end":return a.stop()}}),a)})))()},pullDownRefresh:function(){var t=this;return(0,r.default)((0,i.default)().mark((function e(){var a;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=t.isLastIdPattern?(0,o.default)((0,o.default)({},t.query),{},{last_id:0}):(0,o.default)((0,o.default)({},t.query),{},{page:1}),e.next=3,t.load(a);case 3:case"end":return e.stop()}}),e)})))()},onChangeToScroll:function(){var t=this;this.$nextTick((function(){t.system.pageScrollTo(0,0)}))}},onLoad:function(){this.isLastIdPattern?(delete this.query.page,this.query=(0,o.default)((0,o.default)({},this.query),{},{last_id:0})):this.isListPattern&&delete this.query.limit;var t=this.load;this.load=function(){var e=this,a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.query,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.currentTabIndex;return this.feedback.loading(),t(a,n).then((function(t){var i=t.$pendUpdateObj,r=void 0===i?{}:i;Object.keys(r).forEach((function(t){e[t]=r[t]}));var s=(null===t||void 0===t?void 0:t.data)||{},u=s.total,c=void 0===u?0:u,l=s.last_id,d=s.list,f=a.page,p=a.limit;return e.isLastIdPattern?e.query=(0,o.default)((0,o.default)({},e.query),{},{last_id:l}):(e.isListPattern||(e.totalPage=Math.ceil(c/p)),e.query=a),e.currentTabIndex=n,e.isLastIdPattern?e.reachBottomLoadStatus=l?"loadmore":"nomore":e.isListPattern?e.reachBottomLoadStatus=d&&d.length?"loadmore":"nomore":e.reachBottomLoadStatus=f!=e.totalPage&&e.totalPage?"loadmore":"nomore",e.loading=!1,t})).catch((function(){return e.reachBottomLoadStatus="loadmore",{data:{list:e.list}}})).finally((function(){e.feedback.hideLoading(),uni.stopPullDownRefresh()}))}.bind(this)}};e.default=s},c8f9:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[a("v-uni-view",{staticClass:"flex-c-c"},[a("v-uni-image",{class:t.imgClazz,attrs:{src:t.ossIcon(t.img)}})],1),t.btnText?a("v-uni-view",{staticClass:"flex-c-c",class:t.btnBlockClazz},[a("v-uni-button",{staticClass:"vh-btn flex-c-c w-208 h-64 font-wei-500 font-32 text-e80404 bg-ffffff b-rad-29 b-s-02-e80404",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.btnFun.apply(void 0,arguments)}}},[t._v(t._s(t.btnText))])],1):t._e(),t.title?a("v-uni-view",{staticClass:"font-wei-500 font-36 text-3 l-h-50 text-center",class:t.titleClazz},[t._v(t._s(t.title))]):t._e(),t.desc?a("v-uni-view",{staticClass:"font-28 text-6 l-h-40 text-center",class:t.descClazz},[t._v(t._s(t.desc))]):t._e()],1)},i=[]},ce7c:function(t,e,a){"use strict";a.r(e);var n=a("0efb"),i=a("ea26");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a("eb5f");var r=a("f0c5"),s=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"89d76102",null,!1,n["a"],void 0);e["default"]=s.exports},d72e:function(t,e,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a("e143")),o={props:{img:{type:String,default:"/auction/none_440_400.png"},imgClazz:{type:String,default:"w-440 h-400"},btnText:{type:String,default:""},btnFun:{type:Function,default:function(){var t=i.default.prototype,e=t.$app,a=t.$ios;e&&(a?wineYunJsBridge.openAppPage({client_path:"jumpCommunityAuction"}):wineYunJsBridge.openAppPage({client_path:{android_path:"goMain"},ad_path_param:[{android_key:"type",android_val:"2"},{android_key:"str",android_val:"1"}]}))}},btnBlockClazz:{type:String,default:"mt-68"},title:{type:String,default:""},titleClazz:{type:String,default:"mt-72"},desc:{type:String,default:""},descClazz:{type:String,default:"mt-20"}}};e.default=o},e4d5:function(t,e,a){"use strict";a.r(e);var n=a("8a04"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},e643:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return n}));var n={uLine:a("9ff7").default,uLoading:a("301a").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"u-load-more-wrap",style:{backgroundColor:t.bgColor,marginBottom:t.marginBottom+"rpx",marginTop:t.marginTop+"rpx",height:t.$u.addUnit(t.height)}},[a("u-line",{attrs:{color:"#d4d4d4",length:"50"}}),a("v-uni-view",{staticClass:"u-load-more-inner",class:"loadmore"==t.status||"nomore"==t.status?"u-more":""},[a("v-uni-view",{staticClass:"u-loadmore-icon-wrap"},[a("u-loading",{staticClass:"u-loadmore-icon",attrs:{color:t.iconColor,mode:"circle"==t.iconType?"circle":"flower",show:"loading"==t.status&&t.icon}})],1),a("v-uni-view",{staticClass:"u-line-1",class:["nomore"==t.status&&1==t.isDot?"u-dot-text":"u-more-text"],style:[t.loadTextStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.loadMore.apply(void 0,arguments)}}},[t._v(t._s(t.showText))])],1),a("u-line",{attrs:{color:"#d4d4d4",length:"50"}})],1)},o=[]},e842:function(t,e,a){"use strict";a.r(e);var n=a("34dc"),i=a("b1db");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a("bc18");var r=a("f0c5"),s=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"9ac734a8",null,!1,n["a"],void 0);e["default"]=s.exports},ea26:function(t,e,a){"use strict";a.r(e);var n=a("3dc3"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},eb5f:function(t,e,a){"use strict";var n=a("b252"),i=a.n(n);i.a},f074:function(t,e,a){"use strict";a.r(e);var n=a("7f1a"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},f2f9:function(t,e,a){"use strict";var n=a("a126"),i=a.n(n);i.a},f6b7:function(t,e,a){"use strict";a.r(e);var n=a("c8f9"),i=a("7b0c");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);var r=a("f0c5"),s=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"79787d9f",null,!1,n["a"],void 0);e["default"]=s.exports}}]);