(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageB-pages-payment-payment"],{"00d9":function(e,t,a){var n=a("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-model[data-v-acf792f8]{height:auto;overflow:hidden;font-size:%?32?%;background-color:#fff}.u-model__btn--hover[data-v-acf792f8]{background-color:#e6e6e6}.u-model__title[data-v-acf792f8]{padding-top:%?48?%;font-weight:500;text-align:center;color:#303133}.u-model__content__message[data-v-acf792f8]{padding:%?48?%;font-size:%?30?%;text-align:center;color:#606266}.u-model__footer[data-v-acf792f8]{display:flex;flex-direction:row}.u-model__footer__button[data-v-acf792f8]{flex:1;height:%?100?%;line-height:%?100?%;font-size:%?32?%;box-sizing:border-box;cursor:pointer;text-align:center;border-radius:%?4?%}',""]),e.exports=t},"0402":function(e,t,a){"use strict";a.r(t);var n=a("21e4"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=i.a},"0efb":function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"vh-image-con",class:[e.isMf?"mf-card":""],style:[e.wrapStyle,e.backgroundStyle],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onClick.apply(void 0,arguments)}}},[e.isError?e._e():a("v-uni-image",{staticClass:"vh-image",style:{backgroundColor:e.bgColor,borderRadius:"circle"==e.shape?"50%":e.$u.addUnit(e.borderRadius)},attrs:{src:e.getImage,mode:e.mode,"lazy-load":e.lazyLoad,"show-menu-by-longpress":e.showMenuByLongpress},on:{error:function(t){arguments[0]=t=e.$handleEvent(t),e.onErrorHandler.apply(void 0,arguments)},load:function(t){arguments[0]=t=e.$handleEvent(t),e.onLoadHandler.apply(void 0,arguments)}}}),e.showLoading&&e.loading?a("v-uni-view",{staticClass:"vh-image-loading",style:{borderRadius:"circle"==e.shape?"50%":e.$u.addUnit(e.borderRadius),backgroundColor:this.bgColor}},[a("v-uni-image",{style:[e.wrapStyle],attrs:{src:e.getLoadingImage,mode:e.mode}})],1):e._e(),e.showError&&e.isError&&!e.loading?a("v-uni-view",{staticClass:"u-image-error",style:{borderRadius:"circle"==e.shape?"50%":e.$u.addUnit(e.borderRadius)}},[a("v-uni-image",{style:[e.wrapStyle],attrs:{src:e.getLoadingImage,mode:e.mode}})],1):e._e()],1)},i=[]},"21e4":function(e,t,a){"use strict";a("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a9e3");var n=uni.getSystemInfoSync(),i={},o={name:"u-navbar",props:{height:{type:[String,Number],default:""},backIconColor:{type:String,default:"#606266"},backIconName:{type:String,default:"nav-back"},backIconSize:{type:[String,Number],default:"44"},backText:{type:String,default:""},backTextStyle:{type:Object,default:function(){return{color:"#606266"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleColor:{type:String,default:"#606266"},titleBold:{type:Boolean,default:!1},titleSize:{type:[String,Number],default:32},isBack:{type:[Boolean,String],default:!0},background:{type:Object,default:function(){return{background:"#ffffff"}}},isFixed:{type:Boolean,default:!0},immersive:{type:Boolean,default:!1},borderBottom:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},customBack:{type:Function,default:null}},data:function(){return{menuButtonInfo:i,statusBarHeight:n.statusBarHeight}},computed:{navbarInnerStyle:function(){var e={};return e.height=this.navbarHeight+"px",e},navbarStyle:function(){var e={};return e.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(e,this.background),e},titleStyle:function(){var e={};return e.left=(n.windowWidth-uni.upx2px(this.titleWidth))/2+"px",e.right=(n.windowWidth-uni.upx2px(this.titleWidth))/2+"px",e.width=uni.upx2px(this.titleWidth)+"px",e},navbarHeight:function(){return this.height?this.height:44}},created:function(){},methods:{goBack:function(){"function"===typeof this.customBack?this.customBack.bind(this.$u.$parent.call(this))():uni.navigateBack()}}};t.default=o},2968:function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return n}));var n={uNavbar:a("36f7").default,vhCountDown:a("e7b7").default,uButton:a("4f1b").default,uModal:a("5761").default,uPopup:a("c4b0").default,vhImage:a("ce7c").default},i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return e.payInfo.main_order_no||e.payInfo.order_no?a("v-uni-view",{staticClass:"content h-vh-100 bg-f5f5f5 o-hid"},[a("u-navbar",{attrs:{"is-back":!1,title:"收银台","title-size":"36","title-color":"#fff",background:{background:"#C8101B"}}},[a("v-uni-image",{staticClass:"ml-24 w-44 h-44",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/payment/back_arr.png",mode:"aspectFill"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showLeaveModel=!0}}})],1),a("v-uni-view",{staticClass:"pt-50 h-376 bg-c8101b"},[a("v-uni-view",{staticClass:"d-flex j-center a-center"},[a("v-uni-text",{staticClass:"font-36 font-wei text-ffffff"},[e._v("¥")]),a("v-uni-text",{staticClass:"ml-08 font-72 font-wei text-ffffff"},[e._v(e._s(e.payInfo.payment_amount))])],1),1!=e.payInfo.payPlate?a("v-uni-view",{staticClass:"d-flex j-center mt-08"},[a("v-uni-view",{staticClass:"font-28 text-ffffff"},[e._v("剩余有效时间：")]),a("vh-count-down",{ref:"uCountDown",attrs:{"show-days":!1,timestamp:e.payInfo.countdown,"bg-color":"transparent","font-size":"28","separator-size":"24","separator-color":"#fff",color:"#fff"},on:{end:function(t){arguments[0]=t=e.$handleEvent(t),e.countDownEnd()}}})],1):e._e()],1),a("v-uni-view",{staticClass:"mt-n-72 b-rad-10 b-sh-00041200-013 w-702 bg-ffffff mtb-00-mlr-auto"},e._l(e.threePayTypeList,(function(t,n){return a("v-uni-view",{key:n},[n?a("v-uni-view",{staticClass:"w-638 h-02 bg-eeeeee mtb-00-mlr-auto"}):e._e(),a("v-uni-view",{staticClass:"flex-sb-c ptb-00-plr-32 h-160",on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.threePayType=t.type}}},[a("v-uni-view",{staticClass:"flex-c-c"},[a("v-uni-image",{staticClass:"w-48 h-48",attrs:{src:e.ossIcon(t.icon)}}),a("v-uni-text",{staticClass:"ml-12 font-32 text-3 l-h-44"},[e._v(e._s(t.name))])],1),a("v-uni-image",{class:t.type===e.threePayType?"w-36 h-36":"w-40 h-40",attrs:{src:e.ossIcon(t.type===e.threePayType?"/payment/cir_sel_h_36.png":"/payment/cir_sel_40.png")}})],1)],1)})),1),a("v-uni-view",{staticClass:"flex-c-c mt-136"},[a("u-button",{attrs:{shape:"circle",loading:e.paying,"hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"678rpx",height:"80rpx",fontSize:"28rpx",color:"#FFF",backgroundColor:"#E80404",border:"none"}},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handlePay.apply(void 0,arguments)}}},[e._v(e._s(e.paying?"支付中...":"支付"))])],1),a("v-uni-view",{domProps:{innerHTML:e._s(e.ailPayForm)}}),a("u-modal",{attrs:{width:600,title:"确认要放弃付款吗","title-style":{fontSize:"28rpx",fontWeight:"bold",color:"#333"},"show-cancel-button":!0,"cancel-text":"残忍离开","confirm-text":"继续支付","cancel-style":{fontSize:"28rpx",color:"#999"},"confirm-style":{fontSize:"28rpx",color:"#E80404"},content:"好货不等人,请尽快支付~","content-style":{fontSize:"28rpx",color:"#333"}},on:{cancel:function(t){arguments[0]=t=e.$handleEvent(t),e.leave()},confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.showLeaveModel=!1}},model:{value:e.showLeaveModel,callback:function(t){e.showLeaveModel=t},expression:"showLeaveModel"}}),a("u-popup",{staticClass:"bg-transparent-popup",attrs:{mode:"center",width:"434rpx",height:"520rpx","border-radius":"20"},model:{value:e.toPublicPopupVisible,callback:function(t){e.toPublicPopupVisible=t},expression:"toPublicPopupVisible"}},[a("v-uni-view",{staticClass:"p-rela w-p100 h-p100"},[a("v-uni-image",{staticClass:"p-abso w-p100 h-p100",attrs:{src:e.ossIcon("/payment/co_bg.png")}}),a("v-uni-view",{staticClass:"p-rela"},[a("v-uni-view",{staticClass:"pt-32 font-wei-500 font-28 text-3 l-h-40 text-center"},[a("v-uni-view",[e._v("如需对公付款")]),a("v-uni-view",[e._v("请扫码添加专属客服")])],1),a("v-uni-view",{staticClass:"flex-c-c mt-78"},[a("vh-image",{attrs:{src:e.toPublicKefuQrCode,width:282,height:282,loadingType:2,showMenuByLongpress:!0}})],1)],1)],1)],1)],1):e._e()},o=[]},"301a":function(e,t,a){"use strict";a.r(t);var n=a("3b5c"),i=a("ffc6");for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);a("b515");var r=a("f0c5"),s=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"19cf7fca",null,!1,n["a"],void 0);t["default"]=s.exports},"36f7":function(e,t,a){"use strict";a.r(t);var n=a("95b6"),i=a("0402");for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);a("b10b");var r=a("f0c5"),s=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"2920cc37",null,!1,n["a"],void 0);t["default"]=s.exports},"3b5c":function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){}));var n=function(){var e=this.$createElement,t=this._self._c||e;return this.show?t("v-uni-view",{staticClass:"u-loading",class:"circle"==this.mode?"u-loading-circle":"u-loading-flower",style:[this.cricleStyle]}):this._e()},i=[]},"3dc3":function(e,t,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(a("d0af")),o=n(a("f3f3"));a("a9e3"),a("d3b7"),a("159b"),a("e25e"),a("c975");var r=a("26cb"),s={name:"u-image",props:{loadingType:{type:[String,Number],default:1},isMf:{type:Boolean,default:!1},src:{default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!1},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:100},bgColor:{type:String,default:"transparent"},isResize:{type:Boolean,default:!1},resizeRatio:{type:Object,default:function(){return{wratio:16,hratio:9}}},resizeUsePx:{type:Boolean,default:!1},opacityProp:{type:Number,default:1},backgroundImage:{type:String,default:""}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(e){e?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:(0,o.default)((0,o.default)({},(0,r.mapState)(["ossPrefix"])),{},{wrapStyle:function(){var e={};if(e.width=this.$u.addUnit(this.width),e.height=this.$u.addUnit(this.height),this.backgroundImage&&(e.backgroundImage="url(".concat(this.backgroundImage,")"),e.backgroundSize="100% 100%",e.backgroundRepeat="no-repeat",e.backgroundPosition="center"),this.isResize){var t,a,n=(null===this||void 0===this||null===(t=this.src)||void 0===t||null===(a=t.split("?"))||void 0===a?void 0:a[1])||"",o=n.split("&"),r={};o.forEach((function(e){var t=e.split("="),a=(0,i.default)(t,2),n=a[0],o=a[1];r[n]=o}));var s=+((null===r||void 0===r?void 0:r.w)||""),c=+((null===r||void 0===r?void 0:r.h)||"");if(!isNaN(s)&&!isNaN(c)&&s&&c){var u=parseInt(this.width),l=u/s*c,d=this.resizeRatio,f=d.wratio,p=d.hratio;if("auto"!==f&&"auto"!==p){var h=u*f/p,y=u*p/f;l>h?l=h:l<y&&(l=y)}this.resizeUsePx?e.height="".concat(l,"px"):e.height=this.$u.addUnit(l)}}return e.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),e.overflow=this.borderRadius>0||"circle"==this.shape?"hidden":"visible",this.fade&&(e.opacity=this.opacity,e.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),e},getLoadingImage:function(){return"https://images.vinehoo.com/vinehoomini/v3/occupy_img/new_img".concat(this.loadingType,".png")},getImage:function(){return"string"!==typeof this.src?"":-1==this.src.indexOf("http")?this.ossPrefix+this.src:this.src}}),methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(e){this.loading=!1,this.isError=!0,this.$emit("error",e)},onLoadHandler:function(){var e=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){e.durationTime=e.duration,e.opacity=e.opacityProp,setTimeout((function(){e.removeBgColor()}),e.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};t.default=s},"430f":function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return n}));var n={uPopup:a("c4b0").default,uLoading:a("301a").default},i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",[a("u-popup",{attrs:{zoom:e.zoom,mode:"center",popup:!1,"z-index":e.uZIndex,length:e.width,"mask-close-able":e.maskCloseAble,"border-radius":e.borderRadius,"negative-top":e.negativeTop},on:{close:function(t){arguments[0]=t=e.$handleEvent(t),e.popupClose.apply(void 0,arguments)}},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}},[a("v-uni-view",{staticClass:"u-model"},[e.showTitle?a("v-uni-view",{staticClass:"u-model__title u-line-1",style:[e.titleStyle]},[e._v(e._s(e.title))]):e._e(),a("v-uni-view",{staticClass:"u-model__content"},[e.$slots.default||e.$slots.$default?a("v-uni-view",{style:[e.contentStyle]},[e._t("default")],2):a("v-uni-view",{staticClass:"u-model__content__message",style:[e.contentStyle]},[e._v(e._s(e.content))])],1),e.showCancelButton||e.showConfirmButton?a("v-uni-view",{staticClass:"u-model__footer u-border-top"},[e.showCancelButton?a("v-uni-view",{staticClass:"u-model__footer__button",style:[e.cancelBtnStyle],attrs:{"hover-stay-time":100,"hover-class":"u-model__btn--hover"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.cancel.apply(void 0,arguments)}}},[e._v(e._s(e.cancelText))]):e._e(),e.showConfirmButton||e.$slots["confirm-button"]?a("v-uni-view",{staticClass:"u-model__footer__button hairline-left",style:[e.confirmBtnStyle],attrs:{"hover-stay-time":100,"hover-class":e.asyncClose?"none":"u-model__btn--hover"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.confirm.apply(void 0,arguments)}}},[e.$slots["confirm-button"]?e._t("confirm-button"):[e.loading?a("u-loading",{attrs:{mode:"circle",color:e.confirmColor}}):[e._v(e._s(e.confirmText))]]],2):e._e()],1):e._e()],1)],1)],1)},o=[]},5761:function(e,t,a){"use strict";a.r(t);var n=a("430f"),i=a("70e8");for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);a("6f69");var r=a("f0c5"),s=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"acf792f8",null,!1,n["a"],void 0);t["default"]=s.exports},"59df":function(e,t,a){var n=a("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-navbar[data-v-2920cc37]{width:100%}.u-navbar-fixed[data-v-2920cc37]{position:fixed;left:0;right:0;top:0;z-index:991}.u-status-bar[data-v-2920cc37]{width:100%}.u-navbar-inner[data-v-2920cc37]{display:flex;flex-direction:row;justify-content:space-between;position:relative;align-items:center}.u-back-wrap[data-v-2920cc37]{display:flex;flex-direction:row;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.u-back-text[data-v-2920cc37]{padding-left:%?4?%;font-size:%?30?%}.u-navbar-content-title[data-v-2920cc37]{display:flex;flex-direction:row;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0}.u-navbar-centent-slot[data-v-2920cc37]{flex:1}.u-title[data-v-2920cc37]{line-height:%?60?%;font-size:%?32?%;flex:1}.u-navbar-right[data-v-2920cc37]{flex:1;display:flex;flex-direction:row;align-items:center;justify-content:flex-end}.u-slot-content[data-v-2920cc37]{flex:1;display:flex;flex-direction:row;align-items:center}',""]),e.exports=t},"6ab5":function(e,t,a){var n=a("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-image-con[data-v-89d76102]{position:relative;transition:opacity .5s ease-in}.vh-image[data-v-89d76102]{width:100%;height:100%}.vh-image-loading[data-v-89d76102],\n.u-image-error[data-v-89d76102]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;background-color:#fff;color:#909399;font-size:%?46?%}.mf-card[data-v-89d76102]{background-color:#f7f7f7!important;padding:5px}',""]),e.exports=t},"6bdb":function(e,t,a){var n=a("59df");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var i=a("4f06").default;i("3dc5b1f9",n,!0,{sourceMap:!1,shadowMode:!1})},"6e10":function(e,t,a){var n=a("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-loading-circle[data-v-19cf7fca]{display:inline-flex;vertical-align:middle;width:%?28?%;height:%?28?%;background:0 0;border-radius:50%;border:2px solid;border-color:#e5e5e5 #e5e5e5 #e5e5e5 #8f8d8e;-webkit-animation:u-circle-data-v-19cf7fca 1s linear infinite;animation:u-circle-data-v-19cf7fca 1s linear infinite}.u-loading-flower[data-v-19cf7fca]{width:20px;height:20px;display:inline-block;vertical-align:middle;-webkit-animation:u-flower-data-v-19cf7fca 1s steps(12) infinite;animation:u-flower-data-v-19cf7fca 1s steps(12) infinite;background:transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+PHBhdGggZmlsbD0ibm9uZSIgZD0iTTAgMGgxMDB2MTAwSDB6Ii8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTlFOUU5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAgLTMwKSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iIzk4OTY5NyIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgzMCAxMDUuOTggNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjOUI5OTlBIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDYwIDc1Ljk4IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0EzQTFBMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSg5MCA2NSA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNBQkE5QUEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoMTIwIDU4LjY2IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0IyQjJCMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgxNTAgNTQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjQkFCOEI5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDE4MCA1MCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDMkMwQzEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTE1MCA0NS45OCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDQkNCQ0IiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTEyMCA0MS4zNCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNEMkQyRDIiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTkwIDM1IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0RBREFEQSIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgtNjAgMjQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTJFMkUyIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKC0zMCAtNS45OCA2NSkiLz48L3N2Zz4=) no-repeat;background-size:100%}@-webkit-keyframes u-flower-data-v-19cf7fca{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes u-flower-data-v-19cf7fca{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@-webkit-keyframes u-circle-data-v-19cf7fca{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}',""]),e.exports=t},"6f69":function(e,t,a){"use strict";var n=a("9945"),i=a.n(n);i.a},"70e8":function(e,t,a){"use strict";a.r(t);var n=a("aa64"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=i.a},"95b6":function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return n}));var n={uIcon:a("e5e1").default},i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{},[a("v-uni-view",{staticClass:"u-navbar",class:{"u-navbar-fixed":e.isFixed,"u-border-bottom":e.borderBottom},style:[e.navbarStyle]},[a("v-uni-view",{staticClass:"u-status-bar",style:{height:e.statusBarHeight+"px"}}),a("v-uni-view",{staticClass:"u-navbar-inner",style:[e.navbarInnerStyle]},[e.isBack?a("v-uni-view",{staticClass:"u-back-wrap",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.goBack.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"u-icon-wrap"},[a("u-icon",{attrs:{name:e.backIconName,color:e.backIconColor,size:e.backIconSize}})],1),e.backText?a("v-uni-view",{staticClass:"u-icon-wrap u-back-text u-line-1",style:[e.backTextStyle]},[e._v(e._s(e.backText))]):e._e()],1):e._e(),e.title?a("v-uni-view",{staticClass:"u-navbar-content-title",style:[e.titleStyle]},[a("v-uni-view",{staticClass:"u-title u-line-1",style:{color:e.titleColor,fontSize:e.titleSize+"rpx",fontWeight:e.titleBold?"bold":"normal"}},[e._v(e._s(e.title))])],1):e._e(),a("v-uni-view",{staticClass:"u-slot-content"},[e._t("default")],2),a("v-uni-view",{staticClass:"u-slot-right"},[e._t("right")],2)],1)],1),e.isFixed&&!e.immersive?a("v-uni-view",{staticClass:"u-navbar-placeholder",style:{width:"100%",height:Number(e.navbarHeight)+e.statusBarHeight+"px"}}):e._e()],1)},o=[]},9945:function(e,t,a){var n=a("00d9");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var i=a("4f06").default;i("2b69c872",n,!0,{sourceMap:!1,shadowMode:!1})},a989:function(e,t,a){"use strict";a.r(t);var n=a("2968"),i=a("ab6c");for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);var r=a("f0c5"),s=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"762cd2e1",null,!1,n["a"],void 0);t["default"]=s.exports},aa64:function(e,t,a){"use strict";a("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a9e3");var n={name:"u-modal",props:{value:{type:Boolean,default:!1},zIndex:{type:[Number,String],default:""},title:{type:[String],default:"提示"},width:{type:[Number,String],default:600},content:{type:String,default:"内容"},showTitle:{type:Boolean,default:!0},showConfirmButton:{type:Boolean,default:!0},showCancelButton:{type:Boolean,default:!1},confirmText:{type:String,default:"确认"},cancelText:{type:String,default:"取消"},confirmColor:{type:String,default:"#2979ff"},cancelColor:{type:String,default:"#606266"},borderRadius:{type:[Number,String],default:16},titleStyle:{type:Object,default:function(){return{}}},contentStyle:{type:Object,default:function(){return{}}},cancelStyle:{type:Object,default:function(){return{}}},confirmStyle:{type:Object,default:function(){return{}}},zoom:{type:Boolean,default:!0},asyncClose:{type:Boolean,default:!1},maskCloseAble:{type:Boolean,default:!1},negativeTop:{type:[String,Number],default:0}},data:function(){return{loading:!1}},computed:{cancelBtnStyle:function(){return Object.assign({color:this.cancelColor},this.cancelStyle)},confirmBtnStyle:function(){return Object.assign({color:this.confirmColor},this.confirmStyle)},uZIndex:function(){return this.zIndex?this.zIndex:this.$u.zIndex.popup}},watch:{value:function(e){!0===e&&(this.loading=!1)}},methods:{confirm:function(){this.asyncClose?this.loading=!0:this.$emit("input",!1),this.$emit("confirm")},cancel:function(){var e=this;this.$emit("cancel"),this.$emit("input",!1),setTimeout((function(){e.loading=!1}),300)},popupClose:function(){this.$emit("input",!1)},clearLoading:function(){this.loading=!1}}};t.default=n},ab6c:function(e,t,a){"use strict";a.r(t);var n=a("f223"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=i.a},b10b:function(e,t,a){"use strict";var n=a("6bdb"),i=a.n(n);i.a},b252:function(e,t,a){var n=a("6ab5");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var i=a("4f06").default;i("0c30beb4",n,!0,{sourceMap:!1,shadowMode:!1})},b515:function(e,t,a){"use strict";var n=a("f1e2"),i=a.n(n);i.a},ce7c:function(e,t,a){"use strict";a.r(t);var n=a("0efb"),i=a("ea26");for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);a("eb5f");var r=a("f0c5"),s=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"89d76102",null,!1,n["a"],void 0);t["default"]=s.exports},ea26:function(e,t,a){"use strict";a.r(t);var n=a("3dc3"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=i.a},eb5f:function(e,t,a){"use strict";var n=a("b252"),i=a.n(n);i.a},f1e2:function(e,t,a){var n=a("6e10");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var i=a("4f06").default;i("1e029910",n,!0,{sourceMap:!1,shadowMode:!1})},f223:function(e,t,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("ac1f"),a("00b4"),a("caad6"),a("99af"),a("d81d"),a("b64b"),a("e9c4"),a("4de4"),a("d3b7");var i=n(a("f07e")),o=n(a("c964")),r=n(a("d0af")),s=n(a("f3f3")),c=a("26cb"),u=a("d8be"),l=a("1e48"),d=n(a("097a")),f={WX:1,ALI:2,ToPublic:3},p={type:f.WX,name:"微信支付",icon:"/payment/wx_48.png"},h={type:f.ALI,name:"支付宝支付",icon:"/payment/ali_48.png"},y={type:f.ToPublic,name:"对公付款",icon:"/payment/to_public_48.png"},v={name:"payment",data:function(){return{showLeaveModel:!1,paying:!1,enterCount:0,threePayType:f.WX,threePayTypeList:[p,h,y],payParams:{},h5WxPayOrigin:"https://cloud1-9gkbx77e27bcf5b3-1310518333.tcloudbaseapp.com/vinehoo-pay.html",payment_method:1,isWxProcess:!1,ailPayForm:"",code:"",wxPayOpenId:"",orderNo:"",payPlate:"",toPublicPopupVisible:!1,toPublicKefuQrCode:""}},computed:(0,s.default)((0,s.default)({},(0,c.mapState)(["payInfo","requestPrefix","routeTable"])),{},{hasPayInfo:function(e){var t=e.payInfo,a=e.orderNo,n=t.main_order_no,i=t.order_no;return!!n||!!i||!!a},isAppPay:function(e){var t=e.payInfo;return t.$isAppPay},isIosSmzdmapp:function(){var e=window.navigator.userAgent.toLowerCase();return/iphone_smzdmapp/.test(e)}}),onLoad:function(e){if(this.isWxProcess=this.juageWxProcess(),this.orderNo=e.orderNo,this.payPlate=+e.payPlate,"next"==this.$vhFrom){var t=uni.getStorageSync("nextpayInfo");this.muPayInfo(t),uni.removeStorageSync("nextpayInfo")}if(console.log(this.payInfo),this.hasPayInfo){if(this.payInfo.is_cross){this.muPayInfo((0,s.default)((0,s.default)({},this.payInfo),{},{payPlate:5,$isAppPay:!0,$paySuccessReturnUrl:this.routeTable.pBPaySuccess})),this.payPlate=5}if(this.isWxProcess){if(this.threePayTypeList=[p,y],[4,5].includes(this.payPlate)){if(this.threePayTypeList=[p],[5].includes(this.payPlate)&&(this.threePayTypeList=[p,y]),[5].includes(this.payPlate)&&!this.orderNo){var a,n,i=this.payInfo,o=i.main_order_no,c=i.order_no;null===(a=window)||void 0===a||null===(n=a.history)||void 0===n||n.replaceState(null,"","?orderNo=".concat(o||c,"&payPlate=").concat(this.payPlate))}var u=e.code;if(this.$isDev){if(!u)return void(location.href="https://activity.vinehoo.com/activities-v3/RedirectFetchWxCode?appid=".concat(l.WX_APPID_PROD,"&redirectUrl=").concat(encodeURIComponent(window.location.href.split("#")[0])));this.code=u}else{if(!u)return void(location.href="https://open.weixin.qq.com/connect/oauth2/authorize?appid="+l.WX_APPID_PROD+"&redirect_uri="+encodeURIComponent(window.location.href)+"&response_type=code&scope=snsapi_userinfo&state=1#wechat_redirect");this.code=u}this.loadWxPayOpenId(),this.wxConfig()}this.orderNo&&this.loadOrderInfo()}else[4].includes(this.payPlate)?this.threePayTypeList=[h]:[5].includes(this.payPlate)&&(this.threePayTypeList=[h,y]),this.orderNo&&this.loadOrderInfo();var d=(0,r.default)(this.threePayTypeList,1),f=d[0].type,v=void 0===f?"":f;this.threePayType=v}},onShow:function(){if(this.hasPayInfo)this.enterCount++,this.enterCount>=2&&this.queryOrderStatus();else{var e=this.payInfo.payPlate;6===e?this.jump.appAndMiniJump(0,this.$routeTable.pBOrderDeposit,this.$vhFrom,1):this.jump.appAndMiniJump(0,"/packageE/pages/my-order/my-order?status=1",this.$vhFrom,1)}},methods:(0,s.default)((0,s.default)({},(0,c.mapMutations)(["muPayInfo"])),{},{juageWxProcess:function(){var e=window.navigator.userAgent.toLowerCase();return/micromessenger/.test(e)},handlePay:function(){var e=this;return(0,o.default)((0,i.default)().mark((function t(){var a,n,o,r,s,c;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.feedback.loading({title:"支付中..."}),t.prev=1,e.threePayType!==f.ToPublic){t.next=10;break}return t.next=5,e.$u.api.getPayToPublicKefu();case 5:return a=t.sent,n=a.data.show_qrcode,e.toPublicKefuQrCode=n,e.toPublicPopupVisible=!0,t.abrupt("return");case 10:e.paying=!0,o=e.payInfo,r=o.main_order_no,s=o.order_no,c=o.is_cross,e.payParams={main_order_no:r||s,is_cross:c},t.t0=e.threePayType,t.next=t.t0===f.WX?16:t.t0===f.ALI?27:29;break;case 16:if(!e.isAppPay){t.next=20;break}e.wxPay(),t.next=26;break;case 20:if(!e.isWxProcess){t.next=25;break}return t.next=23,e.wxH5WxPay();case 23:t.next=26;break;case 25:e.h5WxPay();case 26:return t.abrupt("break",29);case 27:return e.isAppPay?e.aliPay():e.h5AliPay(),t.abrupt("break",29);case 29:return t.prev=29,e.paying=!1,e.feedback.hideLoading(),t.finish(29);case 33:case"end":return t.stop()}}),t,null,[[1,,29,33]])})))()},wxH5WxPay:function(){var e=this;return(0,o.default)((0,i.default)().mark((function t(){var a,n,o,r,s,c,u,l,d,f,p,h,y,v,m,g,b,I,w;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.payment_method=2,a=e.payParams,n=a.main_order_no,o=a.is_cross,r=e.payInfo,s=r.winePartyInfo,c=r.pageFullPath,u=void 0===c?"":c,l=s||{},d=l.party_id,f=void 0===d?"":d,p=l.package_id,h=void 0===p?"":p,y=l.gid,v=void 0===y?"":y,m={vhType:e.payInfo.payPlate,vhPartyId:f,vhPackageId:h,vhGid:v,pageFullPath:u},g=Object.keys(m).map((function(e){return"".concat(e,"=").concat(m[e])})).join("&"),b="".concat(location.origin).concat(e.routeTable.pPaySuccessJump,"?").concat(g),I={main_order_no:n,payment_method:2,order_type:2,is_cross:o,return_url:b},t.next=10,e.$u.api.payMethod(I);case 10:w=t.sent,location.href=w.data.h5_pay_info;case 12:case"end":return t.stop()}}),t)})))()},h5WxPay:function(){console.log("111990--h5WxPay"),this.payment_method=4;var e=uni.getStorageSync("loginInfo")||{},t=e.token,a=e.uid,n=this.payParams,i=n.main_order_no,o=n.is_cross,r={main_order_no:i,payment_method:4,order_type:2,is_cross:o,token:t,uid:a,from:5,versionCode:100},s=Object.keys(r).map((function(e){return"".concat(e,"=").concat(r[e])})).join("&");console.log("queryStr",s);var c="".concat(this.h5WxPayOrigin,"?").concat(s);if(this.isIosSmzdmapp)location.href=c;else{var u=window.open("/");u?u.location=c:location.href=c}},h5AliPay:function(){var e=this;return(0,o.default)((0,i.default)().mark((function t(){var a,n,o,r,s,c,u,l,d,f,p,h,y,v;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a="",e.isIosSmzdmapp||(console.log("newTab-----------"),a=window.open("/")),console.log("111990--h5AliPay"),e.payment_method=1,n=e.payParams,o=n.main_order_no,r=n.is_cross,s=location,c=s.origin,u=e.routeTable,l=u.pDWinePartyOrderList,d=u.pEMyOrder,f=u.pBOrderDeposit,p=1===e.payInfo.payPlate?"".concat(c).concat(l):"".concat(c).concat(d,"?myOrderStatus=1"),6===e.payInfo.payPlate&&(p="".concat(c).concat(f)),h={main_order_no:o,payment_method:1,order_type:2,is_cross:r,return_url:p},console.log("return_url------",p),t.next=13,e.$u.api.payMethod(h);case 13:y=t.sent,v=y.data.h5_pay_info,console.log("res.data------",JSON.stringify(y.data)),console.log("h5_pay_info------",v),a?a.location=v:location.href=v;case 18:case"end":return t.stop()}}),t)})))()},aliPay:function(){var e=this;return(0,o.default)((0,i.default)().mark((function t(){var a,n,o,r,s,c,l,d,f;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return console.log("111990--aliPay"),n=e.payInfo,o=n.$paySuccessReturnUrl,r=void 0===o?"":o,s=n.pageFullPath,c=r||s,l=u.MAppPaymentSource.CrossBorder,4===e.payInfo.payPlate&&(l=u.MAppPaymentSource.AuctionOrder),d={source:l,main_order_no:e.payParams.main_order_no,payment_method:u.MPaymentMethod.AliH5,return_url:"".concat(location.origin).concat(c)},t.next=8,e.$u.api.appPayment(d);case 8:f=t.sent,e.feedback.loading({title:"支付中..."}),e.ailPayForm=(null===f||void 0===f||null===(a=f.data)||void 0===a?void 0:a.pay_info)||"",e.$nextTick((function(){var t,a;null===(t=document)||void 0===t||null===(a=t.forms["alipay_submit"])||void 0===a||a.submit(),e.feedback.hideLoading()}));case 12:case"end":return t.stop()}}),t)})))()},wxPay:function(){var e=this;return(0,o.default)((0,i.default)().mark((function t(){var a,n,o,r,s,c,l,f,p;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(console.log("111990--wxPay"),e.wxPayOpenId){t.next=3;break}return t.abrupt("return");case 3:return n=u.MAppPaymentSource.CrossBorder,4===e.payInfo.payPlate&&(n=u.MAppPaymentSource.AuctionOrder),o={source:n,main_order_no:e.payParams.main_order_no,payment_method:u.MPaymentMethod.WxJSAPI,open_id:e.wxPayOpenId},t.next=8,e.$u.api.appPayment(o);case 8:r=t.sent,s=(null===r||void 0===r||null===(a=r.data)||void 0===a?void 0:a.pay_info)||{},c=s.timeStamp,l=s.nonceStr,f=s.signType,p=s.paySign,d.default.chooseWXPay({timestamp:c,nonceStr:l,package:s.package,signType:f,paySign:p});case 12:case"end":return t.stop()}}),t)})))()},loadWxPayOpenId:function(){var e=this;this.$u.api.getWxPayOpenIdByCode({code:this.code,genre:1}).then((function(t){var a;e.wxPayOpenId=(null===t||void 0===t||null===(a=t.data)||void 0===a?void 0:a.openid)||""})).catch((function(){var t=e.pages.getCurrenPage().$page.options,a=Object.keys(t).filter((function(e){return"code"!==e})).map((function(e){return"".concat(e,"=").concat(t[e])})).join("&"),n="".concat(location.pathname).concat(a?"?".concat(a):"");location.href=n}))},wxConfig:function(){var e=this,t=window.location.href.split("#")[0];this.$isDev&&(t="https://activity.vinehoo.com/activities-v3/WechatCode"),this.$u.api.getJsapiSign({url:t,appid:l.WX_APPID_PROD}).then((function(t){var a=t||{},n=a.appid,i=a.noncestr,o=a.sign,r=a.timestamp,s={debug:e.$isDev,appId:n,nonceStr:i,signature:o,timestamp:r,jsApiList:["chooseWXPay"]};console.log("configData",s),d.default.config(s)}))},getQueryUmsPayOrderStatus:function(){var e=this;return(0,o.default)((0,i.default)().mark((function t(){var a,n,o,r,s,c,u,l,d,f,p,h,y,v;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,Object.keys(e.payParams).length){t.next=3;break}return t.abrupt("return");case 3:return e.feedback.loading({title:"查询支付状态中..."}),a=e.payParams.main_order_no,n={order_no:a},t.next=8,e.$u.api.queryCommonOrderDetail(n);case 8:if(o=t.sent,e.feedback.toast({title:"查询成功~"}),r=o.data||{},s=r.sub_order_status,c=void 0===s?0:s,u=e.payInfo.payPlate,l=e.routeTable,d=l.pDWinePartyOrderList,f=l.pBPaySuccess,p=l.pEMyOrder,h="",![1,2,3].includes(c)){t.next=28;break}if(h=1===u?d:f,7!==u){t.next=26;break}if(y=getCurrentPages().length,!(y>1)){t.next=25;break}return v=getCurrentPages()[y-2],v.$vm.coldChainUpgradeStatus=!0,e.jump.jumpPrePage(e.$vhFrom),t.abrupt("return");case 25:h=p;case 26:t.next=29;break;case 28:h=1===u?d:p;case 29:e.jump.appAndMiniJump(0,h,e.$vhFrom,1),t.next=34;break;case 32:t.prev=32,t.t0=t["catch"](0);case 34:case"end":return t.stop()}}),t,null,[[0,32]])})))()},countDownEnd:function(){var e=this.payInfo.payPlate;switch(e){case 3:this.jump.jumpPrePage(this.$vhFrom);break;case 4:this.jump.jumpPrePage(this.$vhFrom);break;case 6:this.jump.appAndMiniJump(0,this.$routeTable.pBOrderDeposit,this.$vhFrom,1);break;default:this.jump.appAndMiniJump(0,this.routeTable.pEMyOrder,this.$vhFrom,1)}},leave:function(){switch(this.payInfo.payPlate){case 1:this.jump.jumpPrePage(this.$vhFrom);break;case 3:this.jump.jumpPrePage(this.$vhFrom);break;case 4:this.jump.jumpPrePage(this.$vhFrom);break;case 6:this.jump.appAndMiniJump(0,this.$routeTable.pBOrderDeposit,this.$vhFrom,1);break;default:console.log("跳转到我的订单----"),this.jump.appAndMiniJump(0,this.routeTable.pEMyOrder,this.$vhFrom,1)}},queryOrderStatus:function(){var e=this.payInfo.payPlate;3===this.payInfo.payPlate?this.queryAuctionEarnestOrderStatus():4===this.payInfo.payPlate?this.queryAuctionGoodsOrderStatus():6===e?this.queryDepositOrderStatus():this.getQueryUmsPayOrderStatus()},queryAuctionEarnestOrderStatus:function(){var e=this;return(0,o.default)((0,i.default)().mark((function t(){var a,n,o,r,s;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,Object.keys(e.payParams).length){t.next=3;break}return t.abrupt("return");case 3:return e.feedback.loading({title:"查询支付状态中..."}),a=e.payParams.main_order_no,n={main_order_no:a},t.next=8,e.$u.api.getAuctionEarnestOrderDetail(n);case 8:o=t.sent,e.feedback.toast({title:"查询成功~"}),r=(null===o||void 0===o?void 0:o.data)||{},s=r.status,void 0===s?0:s,e.jump.jumpPrePage(e.$vhFrom),t.next=16;break;case 14:t.prev=14,t.t0=t["catch"](0);case 16:case"end":return t.stop()}}),t,null,[[0,14]])})))()},queryAuctionGoodsOrderStatus:function(){var e=this;return(0,o.default)((0,i.default)().mark((function t(){var a,n,o,r;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,Object.keys(e.payParams).length){t.next=3;break}return t.abrupt("return");case 3:return t.next=5,e.$u.api.auctionOrderDetail({order_no:e.payParams.main_order_no});case 5:a=t.sent,n=(null===a||void 0===a?void 0:a.data)||{},o=n.order_status,r=void 0===o?0:o,[1,2,3].includes(r)&&e.jump.jumpPrePage(e.$vhFrom),t.next=12;break;case 10:t.prev=10,t.t0=t["catch"](0);case 12:case"end":return t.stop()}}),t,null,[[0,10]])})))()},queryDepositOrderStatus:function(){var e=this;return(0,o.default)((0,i.default)().mark((function t(){var a,n,o,r;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,Object.keys(e.payParams).length){t.next=3;break}return t.abrupt("return");case 3:return t.next=5,e.$u.api.queryCommonOrderDetail({order_no:e.payParams.main_order_no});case 5:a=t.sent,n=(null===a||void 0===a?void 0:a.data)||{},o=n.sub_order_status,r=void 0===o?0:o,[1,3,4].includes(r)?e.jump.appAndMiniJump(0,e.$routeTable.pBOrderDeposit,e.$vhFrom,1):[0].includes(r)&&(e.feedback.toast({title:"您取消了支付~"}),setTimeout((function(){e.jump.jumpPrePage(e.$vhFrom)}),1500)),t.next=12;break;case 10:t.prev=10,t.t0=t["catch"](0);case 12:case"end":return t.stop()}}),t,null,[[0,10]])})))()},loadOrderInfo:function(){var e=this;return(0,o.default)((0,i.default)().mark((function t(){var a,n,o,r,s,c,u,l,d,f;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(4!==e.payPlate){t.next=8;break}return t.next=3,e.$u.api.auctionOrderDetail({order_no:e.orderNo});case 3:a=t.sent,n=(null===a||void 0===a?void 0:a.data)||{},o=n.order_no,r=n.payment_amount,s=n.countdown,e.muPayInfo({payPlate:e.payPlate,main_order_no:o,payment_amount:r,countdown:s,is_cross:0,$isAppPay:!0}),t.next=14;break;case 8:if(5!==e.payPlate){t.next=14;break}return t.next=11,e.$u.api.orderDetail({order_no:e.orderNo});case 11:c=t.sent,u=(null===c||void 0===c?void 0:c.data)||{},l=u.order_no,d=u.payment_amount,f=u.countdown,e.muPayInfo({payPlate:e.payPlate,main_order_no:l,payment_amount:d,countdown:f,is_cross:1,$isAppPay:!0,$paySuccessReturnUrl:e.routeTable.pBPaySuccess});case 14:case"end":return t.stop()}}),t)})))()}})};t.default=v},fcd7:function(e,t,a){"use strict";a("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a9e3");var n={name:"u-loading",props:{mode:{type:String,default:"circle"},color:{type:String,default:"#c7c7c7"},size:{type:[String,Number],default:"34"},show:{type:Boolean,default:!0}},computed:{cricleStyle:function(){var e={};return e.width=this.size+"rpx",e.height=this.size+"rpx","circle"==this.mode&&(e.borderColor="#e4e4e4 #e4e4e4 #e4e4e4 ".concat(this.color?this.color:"#c7c7c7")),e}}};t.default=n},ffc6:function(e,t,a){"use strict";a.r(t);var n=a("fcd7"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=i.a}}]);