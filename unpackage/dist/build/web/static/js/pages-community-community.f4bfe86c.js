(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-community-community","packageB-pages-order-invoice-order-invoice~packageB-pages-rabbit-head-record-rabbit-head-record~pack~8cc4c645","packageC-pages-my-post-my-post~packageC-pages-topic-detail-topic-detail"],{"03d4":function(t,e,n){var i=n("1cda");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("b5839bf0",i,!0,{sourceMap:!1,shadowMode:!1})},"0402":function(t,e,n){"use strict";n.r(e);var i=n("21e4"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},"062a":function(t,e,n){var i=n("aab3");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("2db15654",i,!0,{sourceMap:!1,shadowMode:!1})},"10eb":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},n("d9e2"),n("d401")},"144f":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"d-flex j-center",style:[this.outerEmpConStyle]},[e("v-uni-view",{staticClass:"p-rela",style:[this.innEmpConStyle]},[e("v-uni-image",{staticClass:"w-p100 h-p100",attrs:{src:this.imageSrc,mode:"aspectFill"}}),e("v-uni-view",{staticClass:"p-abso w-p100 d-flex j-center font-28 text-9",style:[this.textStyle]},[this._v(this._s(this.text))])],1)],1)},a=[]},1589:function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("e9c4"),n("d3b7"),n("3ca3"),n("ddb0"),n("99af"),n("d81d");var a=i(n("d0ff")),o=i(n("f07e")),r=i(n("c964")),s=i(n("fc11")),c=i(n("f3f3")),l=i(n("ec3e")),u=n("26cb"),d={components:{postItemList:l.default},name:"community",computed:(0,c.default)({},(0,u.mapState)(["routeTable"])),data:function(){var t;return t={loading:!0,tabsList:[{name:"推荐"},{name:"酒评"},{name:"关注"}],currentTabs:0,topicList:[],communityList:[],unReadTotalNum:0,page:1},(0,s.default)(t,"loading",!0),(0,s.default)(t,"isLoadMore",!1),(0,s.default)(t,"hasMore",!0),(0,s.default)(t,"followList",[]),(0,s.default)(t,"followPage",1),(0,s.default)(t,"followLoading",!1),(0,s.default)(t,"followHasMore",!0),(0,s.default)(t,"wineList",[]),(0,s.default)(t,"winePage",1),(0,s.default)(t,"wineLoading",!1),(0,s.default)(t,"wineHasMore",!0),(0,s.default)(t,"lastLoginInfo",null),t},onLoad:function(t){this.system.setNavigationBarBlack(),this.getCommunityList(),this.getTopicList(),this.getMessageUnreadNum(),this.lastLoginInfo=uni.getStorageSync("loginInfo"),"2"===t.tab&&(this.currentTabs=2,this.getFollowList())},onShow:function(){var t=uni.getStorageSync("loginInfo");if(JSON.stringify(this.lastLoginInfo)!==JSON.stringify(t))switch(console.log("登录状态发生变化，刷新数据"),this.lastLoginInfo=t,this.page=1,this.winePage=1,this.followPage=1,this.communityList=[],this.wineList=[],this.followList=[],this.currentTabs){case 0:Promise.all([this.getTopicList(),this.getCommunityList(!1,!0),this.getMessageUnreadNum()]);break;case 1:this.getWineList(!1,!0);break;case 2:this.getFollowList(!1,!0);break}},methods:{changeTabs:function(t){2!==t||this.login.isLogin(this.$vhFrom,0)?(this.currentTabs=t,2===t?(this.followPage=1,this.followList=[],this.followHasMore=!0,this.getFollowList()):1===t?(this.winePage=1,this.wineList=[],this.wineHasMore=!0,this.getWineList()):0===t&&(this.page=1,this.communityList=[],this.hasMore=!0,this.getCommunityList())):"1"==this.$vhFrom||"2"==this.$vhFrom||"next"==this.$vhFrom?wineYunJsBridge.openAppPage({client_path:{ios_path:"login",android_path:"login"}}):(uni.setStorageSync("login_back_url","/pages/community/community?tab=2"),uni.navigateTo({url:"/pages/login/login"}))},getTopicList:function(){var t=this;return(0,r.default)((0,o.default)().mark((function e(){var n;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.$u.api.getTopicList({type:2,limit:5});case 3:n=e.sent,0===n.error_code?t.topicList=n.data.list:t.$u.toast(n.error_msg),e.next=10;break;case 7:e.prev=7,e.t0=e["catch"](0),console.error("获取话题列表失败:",e.t0);case 10:case"end":return e.stop()}}),e,null,[[0,7]])})))()},refreshTopics:function(){this.getTopicList()},getMessageUnreadNum:function(){var t=this;return(0,r.default)((0,o.default)().mark((function e(){var n;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.login.isLogin(t.$vhFrom,0)){e.next=5;break}return e.next=3,t.$u.api.messageUnreadNum();case 3:n=e.sent,t.unReadTotalNum=n.data.total_num;case 5:case"end":return e.stop()}}),e)})))()},getCommunityList:function(){var t=arguments,e=this;return(0,r.default)((0,o.default)().mark((function n(){var i,r,s,c;return(0,o.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(i=t.length>0&&void 0!==t[0]&&t[0],r=t.length>1&&void 0!==t[1]&&t[1],!e.isLoadMore&&(e.hasMore||r)){n.next=4;break}return n.abrupt("return",Promise.resolve());case 4:return n.prev=4,e.isLoadMore=!0,console.log("开始加载数据，页码：",e.page),n.next=9,e.$u.api.getCommunityList({page:r?1:e.page,limit:5});case 9:s=n.sent,0===s.error_code?(c=s.data.list||[],console.log("获取到数据条数：",c.length),console.log("返回的数据：",s.data),r?(e.page=1,e.communityList=c,e.hasMore=!0):e.communityList=i?[].concat((0,a.default)(e.communityList),(0,a.default)(c)):c,s.data.total?e.hasMore=e.communityList.length<s.data.total:e.hasMore=c.length>=5,console.log("是否还有更多：",e.hasMore,"当前列表总长度：",e.communityList.length)):e.$u.toast(s.error_msg),n.next=16;break;case 13:n.prev=13,n.t0=n["catch"](4),console.error("获取社区列表失败:",n.t0);case 16:return n.prev=16,e.isLoadMore=!1,e.loading=!1,n.finish(16);case 20:case"end":return n.stop()}}),n,null,[[4,13,16,20]])})))()},onReachBottom:function(){console.log("触发底部加载",{currentTabs:this.currentTabs,hasMore:this.hasMore,isLoadMore:this.isLoadMore}),2===this.currentTabs?this.followHasMore&&!this.followLoading&&(this.followPage++,this.getFollowList(!0)):1===this.currentTabs?this.wineHasMore&&!this.wineLoading&&(this.winePage++,this.getWineList(!0)):this.hasMore&&!this.isLoadMore&&(this.page++,this.getCommunityList(!0))},getFollowList:function(){var t=arguments,e=this;return(0,r.default)((0,o.default)().mark((function n(){var i,r,s,c;return(0,o.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(i=t.length>0&&void 0!==t[0]&&t[0],r=t.length>1&&void 0!==t[1]&&t[1],!e.followLoading&&(e.followHasMore||r)){n.next=4;break}return n.abrupt("return",Promise.resolve());case 4:return n.prev=4,e.followLoading=!0,n.next=8,e.$u.api.getCommunityList({page:r?1:e.followPage,limit:5,list_type:1});case 8:s=n.sent,0===s.error_code?(c=s.data.list||[],r?(e.followPage=1,e.followList=c,e.followHasMore=!0):e.followList=i?[].concat((0,a.default)(e.followList),(0,a.default)(c)):c,e.followHasMore=5===c.length):e.$u.toast(s.error_msg||"获取关注列表失败"),n.next=15;break;case 12:n.prev=12,n.t0=n["catch"](4),console.error("获取关注列表失败:",n.t0);case 15:return n.prev=15,e.followLoading=!1,n.finish(15);case 18:case"end":return n.stop()}}),n,null,[[4,12,15,18]])})))()},getWineList:function(){var t=arguments,e=this;return(0,r.default)((0,o.default)().mark((function n(){var i,r,s,l;return(0,o.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(i=t.length>0&&void 0!==t[0]&&t[0],r=t.length>1&&void 0!==t[1]&&t[1],!e.wineLoading&&(e.wineHasMore||r)){n.next=4;break}return n.abrupt("return",Promise.resolve());case 4:return n.prev=4,e.wineLoading=!0,n.next=8,e.$u.api.wineEvaluationList({page:r?1:e.winePage,limit:10,type:2});case 8:s=n.sent,0===s.error_code?(l=s.data.list||[],l=l.map((function(t){return(0,c.default)((0,c.default)({},t),{},{type_data:t.type_data||"",content:t.wine_evaluation||t.content,diggnums:t.diggnums||t.likenums,viewnums:t.viewnums||0,is_digg:t.is_digg||0,video_url:"",is_attention:t.userinfo.is_follow?1:0,cover_img:""})})),r?(e.winePage=1,e.wineList=l,e.wineHasMore=!0):e.wineList=i?[].concat((0,a.default)(e.wineList),(0,a.default)(l)):l,e.wineHasMore=10===l.length):e.$u.toast(s.error_msg||"获取酒评列表失败"),n.next=16;break;case 12:n.prev=12,n.t0=n["catch"](4),console.error("获取酒评列表失败:",n.t0),e.$u.toast("获取酒评列表失败");case 16:return n.prev=16,e.wineLoading=!1,n.finish(16);case 19:case"end":return n.stop()}}),n,null,[[4,12,16,19]])})))()},goToTopicDetail:function(t){t&&this.jump.appAndMiniJump(1,"/packageC/pages/topic-detail/topic-detail?id=".concat(t),this.$vhFrom)},onPullDownRefresh:function(){switch(this.currentTabs){case 0:Promise.all([this.getTopicList(),this.getCommunityList(!1,!0)]).then((function(){uni.stopPullDownRefresh()}));break;case 1:this.getWineList(!1,!0).then((function(){uni.stopPullDownRefresh()}));break;case 2:this.getFollowList(!1,!0).then((function(){uni.stopPullDownRefresh()}));break}}},created:function(){},onPullDownRefresh:function(){return!0},navigationBarTitleText:"社区",enablePullDownRefresh:!0,backgroundTextStyle:"dark"};e.default=d},"19d1":function(t,e,n){"use strict";n.r(e);var i=n("913e"),a=n("94c5");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("cfe0");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"5fc86f2e",null,!1,i["a"],void 0);e["default"]=s.exports},"1cda":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-badge[data-v-5fc86f2e]{display:inline-flex;justify-content:center;align-items:center;line-height:%?24?%;padding:%?4?% %?8?%;border-radius:%?100?%;z-index:9}.u-badge--bg--primary[data-v-5fc86f2e]{background-color:#2979ff}.u-badge--bg--error[data-v-5fc86f2e]{background-color:#fa3534}.u-badge--bg--success[data-v-5fc86f2e]{background-color:#19be6b}.u-badge--bg--info[data-v-5fc86f2e]{background-color:#909399}.u-badge--bg--warning[data-v-5fc86f2e]{background-color:#f90}.u-badge-dot[data-v-5fc86f2e]{height:%?16?%;width:%?16?%;border-radius:%?100?%;line-height:1}.u-badge-mini[data-v-5fc86f2e]{-webkit-transform:scale(.8);transform:scale(.8);-webkit-transform-origin:center center;transform-origin:center center}.u-info[data-v-5fc86f2e]{background-color:#909399;color:#fff}',""]),t.exports=e},"21e4":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var i=uni.getSystemInfoSync(),a={},o={name:"u-navbar",props:{height:{type:[String,Number],default:""},backIconColor:{type:String,default:"#606266"},backIconName:{type:String,default:"nav-back"},backIconSize:{type:[String,Number],default:"44"},backText:{type:String,default:""},backTextStyle:{type:Object,default:function(){return{color:"#606266"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleColor:{type:String,default:"#606266"},titleBold:{type:Boolean,default:!1},titleSize:{type:[String,Number],default:32},isBack:{type:[Boolean,String],default:!0},background:{type:Object,default:function(){return{background:"#ffffff"}}},isFixed:{type:Boolean,default:!0},immersive:{type:Boolean,default:!1},borderBottom:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},customBack:{type:Function,default:null}},data:function(){return{menuButtonInfo:a,statusBarHeight:i.statusBarHeight}},computed:{navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),t},titleStyle:function(){var t={};return t.left=(i.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(i.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44}},created:function(){},methods:{goBack:function(){"function"===typeof this.customBack?this.customBack.bind(this.$u.$parent.call(this))():uni.navigateBack()}}};e.default=o},"36f7":function(t,e,n){"use strict";n.r(e);var i=n("95b6"),a=n("0402");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("b10b");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"2920cc37",null,!1,i["a"],void 0);e["default"]=s.exports},4053:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if(Array.isArray(t))return(0,i.default)(t)};var i=function(t){return t&&t.__esModule?t:{default:t}}(n("b680"))},"4f1b":function(t,e,n){"use strict";n.r(e);var i=n("825d"),a=n("8e1d");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("fa94");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"4ed92bb2",null,!1,i["a"],void 0);e["default"]=s.exports},"55c2":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var i={name:"vh-empty",props:{bgColor:{type:String,default:"#FFF"},paddingTop:{type:[String,Number],default:0},paddingBottom:{type:[String,Number],default:0},width:{type:[String,Number],default:440},height:{type:[String,Number],default:360},imageSrc:{type:String,default:""},textBottom:{type:[String,Number],default:46},text:{type:String,default:""}},computed:{outerEmpConStyle:function(){return{backgroundColor:this.bgColor,paddingTop:this.paddingTop+"rpx",paddingBottom:this.paddingBottom+"rpx"}},innEmpConStyle:function(){return{width:this.width+"rpx",height:this.height+"rpx"}},textStyle:function(){return{bottom:this.textBottom+"rpx"}}}};e.default=i},"59df":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-navbar[data-v-2920cc37]{width:100%}.u-navbar-fixed[data-v-2920cc37]{position:fixed;left:0;right:0;top:0;z-index:991}.u-status-bar[data-v-2920cc37]{width:100%}.u-navbar-inner[data-v-2920cc37]{display:flex;flex-direction:row;justify-content:space-between;position:relative;align-items:center}.u-back-wrap[data-v-2920cc37]{display:flex;flex-direction:row;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.u-back-text[data-v-2920cc37]{padding-left:%?4?%;font-size:%?30?%}.u-navbar-content-title[data-v-2920cc37]{display:flex;flex-direction:row;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0}.u-navbar-centent-slot[data-v-2920cc37]{flex:1}.u-title[data-v-2920cc37]{line-height:%?60?%;font-size:%?32?%;flex:1}.u-navbar-right[data-v-2920cc37]{flex:1;display:flex;flex-direction:row;align-items:center;justify-content:flex-end}.u-slot-content[data-v-2920cc37]{flex:1;display:flex;flex-direction:row;align-items:center}',""]),t.exports=e},"5ba4":function(t,e,n){"use strict";n.r(e);var i=n("144f"),a=n("a58c");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"55488dce",null,!1,i["a"],void 0);e["default"]=s.exports},6188:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.wine-data-title[data-v-61dc3b9d]{font-weight:400;font-size:%?20?%;color:#333;line-height:%?40?%;\n  /* 添加以下样式实现单行省略 */overflow:hidden;text-overflow:ellipsis;white-space:nowrap;\n  /* 让元素占用剩余空间 */flex:1}.wine-data-bottom-title[data-v-61dc3b9d]{font-weight:400;font-size:%?24?%;color:#666;line-height:%?34?%}.wine-value[data-v-61dc3b9d]{font-weight:400;font-size:%?24?%;color:#333;line-height:%?34?%;max-width:%?400?%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.topic-tags-wrapper[data-v-61dc3b9d]{display:flex;flex-wrap:wrap}.topic-tag[data-v-61dc3b9d]{display:inline-block}.topic-container[data-v-61dc3b9d]{display:block;width:100%;word-break:break-all;word-wrap:break-word}.topic-item[data-v-61dc3b9d]{display:inline-block;margin-bottom:%?16?%}.image-wrapper[data-v-61dc3b9d]{position:relative;overflow:hidden}.image-overlay[data-v-61dc3b9d]{position:absolute;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,.1);border-radius:%?10?%}.remaining-count[data-v-61dc3b9d]{color:#fff;font-size:%?36?%;font-weight:700}\n/* 添加以下样式来隐藏关闭按钮 */[data-v-61dc3b9d] .uni-popup .uni-popup__mask{background-color:rgba(0,0,0,.7)}[data-v-61dc3b9d] .uni-popup .uni-popup__wrapper{background-color:initial}[data-v-61dc3b9d] .uni-image-viewer__close{display:none!important}[data-v-61dc3b9d] .uni-image-viewer__btn-close,[data-v-61dc3b9d] .close-btn,[data-v-61dc3b9d] .wx-image-viewer__close{display:none!important}[data-v-61dc3b9d] .uni-preview-close{display:none!important}[data-v-61dc3b9d] .uni-preview__close{display:none!important}.reject-icon-post[data-v-61dc3b9d]{position:absolute;font-size:%?24?%;right:%?0?%;background-color:#a7a4a4;padding:%?6?% %?14?%;color:#fff;border-radius:0 10px 0 10px;top:0}',""]),t.exports=e},"66f2":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */uni-view[data-v-38852b38],\nuni-scroll-view[data-v-38852b38]{box-sizing:border-box}[data-v-38852b38]::-webkit-scrollbar,[data-v-38852b38]::-webkit-scrollbar,[data-v-38852b38]::-webkit-scrollbar{display:none;width:0!important;height:0!important;-webkit-appearance:none;background:transparent}.u-scroll-box[data-v-38852b38]{position:relative}uni-scroll-view[data-v-38852b38]  ::-webkit-scrollbar{display:none;width:0!important;height:0!important;-webkit-appearance:none;background:transparent}.u-scroll-view[data-v-38852b38]{width:100%;white-space:nowrap;position:relative}.u-tab-item[data-v-38852b38]{position:relative;display:inline-block;text-align:center;transition-property:background-color,color}.u-tab-bar[data-v-38852b38]{position:absolute;bottom:0}.u-tabs-scroll-flex[data-v-38852b38]{display:flex;flex-direction:row;justify-content:space-between}',""]),t.exports=e},"6bdb":function(t,e,n){var i=n("59df");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("3dc5b1f9",i,!0,{sourceMap:!1,shadowMode:!1})},"7a86":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={uBadge:n("19d1").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"u-tabs",style:{background:t.bgColor}},[n("v-uni-view",[n("v-uni-scroll-view",{staticClass:"u-scroll-view",attrs:{"scroll-x":!0,"scroll-left":t.scrollLeft,"scroll-with-animation":!0}},[n("v-uni-view",{staticClass:"u-scroll-box",class:{"u-tabs-scroll-flex":!t.isScroll},attrs:{id:t.id}},[t._l(t.list,(function(e,i){return n("v-uni-view",{key:i,staticClass:"u-tab-item u-line-1",style:[t.tabItemStyle(i)],attrs:{id:"u-tab-item-"+i},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickTab(i)}}},[n("u-badge",{attrs:{count:e[t.count]||e["count"]||0,offset:t.offset,size:"mini"}}),t._v(t._s(e[t.name]||e["name"]))],1)})),t.showBar?n("v-uni-view",{staticClass:"u-tab-bar",style:[t.tabBarStyle]}):t._e()],2)],1)],1)],1)},o=[]},"814f":function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("f07e")),o=i(n("c964"));n("a9e3"),n("ac1f");var r={name:"u-tabs",props:{isScroll:{type:Boolean,default:!0},list:{type:Array,default:function(){return[]}},current:{type:[Number,String],default:0},height:{type:[String,Number],default:80},fontSize:{type:[String,Number],default:30},duration:{type:[String,Number],default:.5},activeColor:{type:String,default:"#2979ff"},inactiveColor:{type:String,default:"#303133"},barWidth:{type:[String,Number],default:40},barHeight:{type:[String,Number],default:6},gutter:{type:[String,Number],default:30},bgColor:{type:String,default:"#ffffff"},name:{type:String,default:"name"},count:{type:String,default:"count"},offset:{type:Array,default:function(){return[5,20]}},bold:{type:Boolean,default:!0},activeItemStyle:{type:Object,default:function(){return{}}},showBar:{type:Boolean,default:!0},barStyle:{type:Object,default:function(){return{}}},itemWidth:{type:[Number,String],default:"auto"}},data:function(){return{scrollLeft:0,tabQueryInfo:[],componentWidth:0,scrollBarLeft:0,parentLeft:0,id:this.$u.guid(),currentIndex:this.current,barFirstTimeMove:!0}},watch:{list:function(t,e){var n=this;t.length!==e.length&&(this.currentIndex=0),this.$nextTick((function(){n.init()}))},current:{immediate:!0,handler:function(t,e){var n=this;this.$nextTick((function(){n.currentIndex=t,n.scrollByIndex()}))}}},computed:{tabBarStyle:function(){var t={width:this.barWidth+"rpx",transform:"translate(".concat(this.scrollBarLeft,"px, -100%)"),"transition-duration":"".concat(this.barFirstTimeMove?0:this.duration,"s"),"background-color":this.activeColor,height:this.barHeight+"rpx",opacity:this.barFirstTimeMove?0:1,"border-radius":"".concat(this.barHeight/2,"px")};return Object.assign(t,this.barStyle),t},tabItemStyle:function(){var t=this;return function(e){var n={height:t.height+"rpx","line-height":t.height+"rpx","font-size":t.fontSize+"rpx","transition-duration":"".concat(t.duration,"s"),padding:t.isScroll?"0 ".concat(t.gutter,"rpx"):"",flex:t.isScroll?"auto":"1",width:t.$u.addUnit(t.itemWidth)};return e==t.currentIndex&&t.bold&&(n.fontWeight="bold"),e==t.currentIndex?(n.color=t.activeColor,n=Object.assign(n,t.activeItemStyle)):n.color=t.inactiveColor,n}}},methods:{init:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var n;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$uGetRect("#"+t.id);case 2:n=e.sent,t.parentLeft=n.left,t.componentWidth=n.width,t.getTabRect();case 6:case"end":return e.stop()}}),e)})))()},clickTab:function(t){t!=this.currentIndex&&this.$emit("change",t)},getTabRect:function(){for(var t=uni.createSelectorQuery().in(this),e=0;e<this.list.length;e++)t.select("#u-tab-item-".concat(e)).fields({size:!0,rect:!0});t.exec(function(t){this.tabQueryInfo=t,this.scrollByIndex()}.bind(this))},scrollByIndex:function(){var t=this,e=this.tabQueryInfo[this.currentIndex];if(e){var n=e.width,i=e.left-this.parentLeft,a=i-(this.componentWidth-n)/2;this.scrollLeft=a<0?0:a;var o=e.left+e.width/2-this.parentLeft;this.scrollBarLeft=o-uni.upx2px(this.barWidth)/2,1==this.barFirstTimeMove&&setTimeout((function(){t.barFirstTimeMove=!1}),100)}}},mounted:function(){this.init()}};e.default=r},"825d":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-button",{staticClass:"u-btn u-line-1 u-fix-ios-appearance",class:["u-size-"+t.size,t.plain?"u-btn--"+t.type+"--plain":"",t.loading?"u-loading":"","circle"==t.shape?"u-round-circle":"",t.hairLine?t.showHairLineBorder:"u-btn--bold-border","u-btn--"+t.type,t.disabled?"u-btn--"+t.type+"--disabled":""],style:[t.customStyle,{overflow:t.ripple?"hidden":"visible"}],attrs:{id:"u-wave-btn","hover-start-time":Number(t.hoverStartTime),"hover-stay-time":Number(t.hoverStayTime),disabled:t.disabled,"form-type":t.formType,"open-type":t.openType,"app-parameter":t.appParameter,"hover-stop-propagation":t.hoverStopPropagation,"send-message-title":t.sendMessageTitle,"send-message-path":"sendMessagePath",lang:t.lang,"data-name":t.dataName,"session-from":t.sessionFrom,"send-message-img":t.sendMessageImg,"show-message-card":t.showMessageCard,"hover-class":t.getHoverClass,loading:t.loading},on:{getphonenumber:function(e){arguments[0]=e=t.$handleEvent(e),t.getphonenumber.apply(void 0,arguments)},getuserinfo:function(e){arguments[0]=e=t.$handleEvent(e),t.getuserinfo.apply(void 0,arguments)},error:function(e){arguments[0]=e=t.$handleEvent(e),t.error.apply(void 0,arguments)},opensetting:function(e){arguments[0]=e=t.$handleEvent(e),t.opensetting.apply(void 0,arguments)},launchapp:function(e){arguments[0]=e=t.$handleEvent(e),t.launchapp.apply(void 0,arguments)},click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.click(e)}}},[t._t("default"),t.ripple?n("v-uni-view",{staticClass:"u-wave-ripple",class:[t.waveActive?"u-wave-active":""],style:{top:t.rippleTop+"px",left:t.rippleLeft+"px",width:t.fields.targetWidth+"px",height:t.fields.targetWidth+"px","background-color":t.rippleBgColor||"rgba(0, 0, 0, 0.15)"}}):t._e()],2)},a=[]},8537:function(t,e,n){"use strict";var i=n("a6e5"),a=n.n(i);a.a},"8d14":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={uNavbar:n("36f7").default,uTabs:n("b14f").default,uIcon:n("e5e1").default,uButton:n("4f1b").default,vhEmpty:n("5ba4").default,vhSkeleton:n("591b").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"content h-min-vh-100 bg-f5f5f5",class:t.loading?"h-vh-100 o-hid":""},[n("v-uni-view",{staticClass:"o-hid"},[n("u-navbar",{attrs:{"is-back":!1}},[n("v-uni-view",{staticClass:"ml-12"},[n("u-tabs",{ref:"uTabs",attrs:{list:t.tabsList,current:t.currentTabs,height:92,"bg-color":{background:"transparent"},"font-size":36,"inactive-color":"#999","active-color":"#E80404",offset:[15,40],"bar-width":36,"bar-height":8,"bar-style":{background:"linear-gradient(214deg, #FF8383 0%, #E70000 100%)"}},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.changeTabs.apply(void 0,arguments)}}})],1),n("v-uni-view",{staticClass:"d-flex a-center",attrs:{slot:"right"},slot:"right"},[n("v-uni-view",{},[n("v-uni-image",{staticClass:"mr-20 w-40 h-40",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/search-comm.png",mode:"aspectFill"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.jump.appAndMiniJump(1,t.routeTable.pFGlobalSearch+"?type=3",t.$vhFrom)}}})],1),n("v-uni-view",{staticClass:"p-rela",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.jump.appAndMiniJump(1,""+t.routeTable.pEMessageCenter,t.$vhFrom,0,!0)}}},[n("v-uni-image",{staticClass:"mr-24 w-40 h-40",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/message-comm.png",mode:"widthFix"}}),t.unReadTotalNum?n("v-uni-view",{staticClass:"p-abso right-24 top-0 w-10 h-10 bg-e80404 b-rad-p50 d-flex j-center a-center font-18 text-ffffff"}):t._e()],1)],1)],1)],1),t.loading?n("v-uni-view",{staticClass:"fade-in"},[n("vh-skeleton",{attrs:{type:6,"has-tab-bar":!0}})],1):n("v-uni-view",{staticClass:"fade-in"},[n("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:0==t.currentTabs,expression:"currentTabs == 0"}]},[t._e(),n("v-uni-view",{staticClass:"bg-ffffff b-rad-20 mt-20 ml-24 mr-24 pt-16 pr-24 pb-10 pl-24"},[n("v-uni-view",{staticClass:"d-flex j-sb a-center"},[n("v-uni-view",{staticClass:"d-flex a-center"},[n("v-uni-image",{staticClass:"w-44 h-44",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/community/red_top_ico.png",mode:"aspectFill"}}),n("v-uni-text",{staticClass:"ml-10 font-32 font-wei text-3 l-h-52"},[t._v("热门话题")])],1),n("v-uni-view",{staticClass:"d-flex a-center",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.jump.appAndMiniJump(1,""+t.routeTable.pCTopicMore,t.$vhFrom)}}},[n("v-uni-text",{staticClass:"mr-10 font-24 text-9"},[t._v("更多话题")]),n("u-icon",{attrs:{name:"arrow-right",size:20,color:"#999"}})],1)],1),n("v-uni-view",{staticClass:"top-list d-flex flex-wrap a-center mt-18"},[t._l(t.topicList,(function(e,i){return n("v-uni-view",{key:i,staticClass:"w-p50 d-flex a-center mb-20",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.goToTopicDetail(e.id)}}},[n("v-uni-view",{staticClass:"w-max-252 font-28 text-3 o-hid text-hidden-1"},[t._v(t._s(e.title))]),e.isrecommend?n("v-uni-image",{staticClass:"ml-20 w-44 h-26",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/new.png",mode:"aspectFill"}}):t._e(),e.ishot?n("v-uni-image",{staticClass:"ml-10 w-46 h-28",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/hot.png",mode:"aspectFill"}}):t._e()],1)})),n("v-uni-view",{staticClass:"d-flex a-center mb-20"},[n("v-uni-text",{staticClass:"bg-e2ebfa b-rad-20 ptb-02-plr-22 font-22 text-2e7bff l-h-32",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.refreshTopics.apply(void 0,arguments)}}},[t._v("换一换")])],1)],2)],1),t.communityList.length>0?[n("postItemList",{attrs:{list:t.communityList,isFollowList:!1}})]:n("vh-empty",{attrs:{bgColor:"transparent","padding-top":100,"padding-bottom":100,"image-src":t.ossIcon("/empty/emp_goods.png"),text:"暂无数据","text-bottom":0}})],2),n("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:1==t.currentTabs,expression:"currentTabs == 1"}]},[t.wineList.length>0?[n("postItemList",{attrs:{list:t.wineList,isFollowList:!1}})]:n("vh-empty",{attrs:{bgColor:"transparent","padding-top":100,"padding-bottom":100,"image-src":t.ossIcon("/empty/emp_goods.png"),text:"暂无数据","text-bottom":0}})],2),n("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:2==t.currentTabs,expression:"currentTabs == 2"}]},[t.followList.length>0?[n("postItemList",{attrs:{list:t.followList,isFollowList:!1}})]:n("vh-empty",{attrs:{bgColor:"transparent","padding-top":100,"padding-bottom":100,"image-src":t.ossIcon("/empty/emp_goods.png"),text:"暂无数据","text-bottom":0}})],2)],1)],1)},o=[]},"8d94":function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("d81d"),n("2ca0"),n("c740"),n("a630"),n("3ca3"),n("99af"),n("d3b7"),n("159b"),n("e25e"),n("e9c4"),n("4de4");var a=i(n("f07e")),o=i(n("d0ff")),r=i(n("c964")),s=i(n("f3f3")),c=n("26cb"),l={props:{myPostSelf:{type:Boolean,default:!1},list:{type:Array,default:function(){return[]}},isFollowList:{type:Boolean,default:!1}},data:function(){return{localList:[],currentUid:"",showPostOpt:!1,currentPost:null}},created:function(){var t=uni.getStorageSync("loginInfo");this.currentUid=t?t.uid:""},computed:(0,s.default)({},(0,c.mapState)(["routeTable","ossPrefix"])),methods:{getImageArray:function(t){var e=this;return t?t.split(",").map((function(t){return t.startsWith("http")?t:e.ossPrefix+t})):[]},handleLike:function(t){var e=this;return(0,r.default)((0,a.default)().mark((function n(){return(0,a.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:e.login.isLoginV3(e.$vhFrom,1).then(function(){var n=(0,r.default)((0,a.default)().mark((function n(i){var r,c,l,u;return(0,a.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!i){n.next=20;break}if(!t.is_digg){n.next=3;break}return n.abrupt("return");case 3:if(r={id:t.id,type:0,action:0},n.prev=4,6!==t.source){n.next=11;break}return n.next=8,e.$u.api.wineEvaluationLike(r);case 8:c=n.sent,n.next=14;break;case 11:return n.next=13,e.$u.api.postsLike(r);case 13:c=n.sent;case 14:0===c.error_code&&(l=e.localList.findIndex((function(e){return e.id===t.id})),-1!==l&&(u=(0,o.default)(e.localList),u[l]=(0,s.default)((0,s.default)({},u[l]),{},{diggnums:t.diggnums+1,is_digg:!0}),e.localList=u)),n.next=20;break;case 17:n.prev=17,n.t0=n["catch"](4),e.$toast("点赞失败");case 20:case"end":return n.stop()}}),n,null,[[4,17]])})));return function(t){return n.apply(this,arguments)}}());case 1:case"end":return n.stop()}}),n)})))()},handleFollow:function(t,e){var n=this;return(0,r.default)((0,a.default)().mark((function i(){return(0,a.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(!t.is_attention){i.next=4;break}uni.showModal({title:"提示",content:"确定取消关注该用户吗？",success:function(){var i=(0,r.default)((0,a.default)().mark((function i(o){return(0,a.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(!o.confirm){i.next=3;break}return i.next=3,n.doFollow(t,e);case 3:case"end":return i.stop()}}),i)})));return function(t){return i.apply(this,arguments)}}()}),i.next=6;break;case 4:return i.next=6,n.doFollow(t,e);case 6:case"end":return i.stop()}}),i)})))()},doFollow:function(t,e){var n=this;return(0,r.default)((0,a.default)().mark((function e(){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:n.login.isLoginV3(n.$vhFrom,1).then(function(){var e=(0,r.default)((0,a.default)().mark((function e(i){var o,r,c;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!i){e.next=12;break}return e.prev=1,o={operate_uid:t.userinfo.uid,status:t.is_attention?2:1},e.next=5,n.$u.api.focusUser(o);case 5:r=e.sent,0==r.error_code&&(n.$u.toast(t.is_attention?"取消关注成功":"关注成功"),c=t.is_attention?0:1,n.localList=n.localList.map((function(e){var n;return(null===(n=e.userinfo)||void 0===n?void 0:n.uid)===t.userinfo.uid?(0,s.default)((0,s.default)({},e),{},{is_attention:c}):e})),uni.$emit("updateFollowStatus",{uid:t.userinfo.uid,status:c}),n.isFollowList&&!c&&(n.$parent.followPage=1,n.$parent.followList=[],n.$parent.followHasMore=!0,n.$parent.getFollowList())),e.next=12;break;case 9:e.prev=9,e.t0=e["catch"](1),console.error("关注操作错误:",e.t0);case 12:case"end":return e.stop()}}),e,null,[[1,9]])})));return function(t){return e.apply(this,arguments)}}());case 1:case"end":return e.stop()}}),e)})))()},previewImage:function(t,e){uni.previewImage({urls:t,current:e,loop:!0,indicator:"number",showCloseBtn:!1,longPressActions:{itemList:["发送给朋友","保存图片","收藏"],success:function(t){console.log("选中了第"+(t.tapIndex+1)+"个按钮")},fail:function(t){console.log(t.errMsg)}}})},goToWineDetail:function(t){var e=this.localList[t].wine_data.period;this.jump.appAndMiniJump(1,"/pages/goods-detail/goods-detail?id=".concat(e),this.$vhFrom)},getStarArray:function(t){var e,n=(null===t||void 0===t||null===(e=t.wine_data)||void 0===e?void 0:e.grade)||0;return Array.from({length:5},(function(t,e){return{index:e,active:e<n}}))},getFormatTaste:function(t){var e,n=null===t||void 0===t||null===(e=t.wine_data)||void 0===e?void 0:e.taste;return n&&Array.isArray(n)?n.map((function(t){var e=Math.floor(t.value/25);return"".concat(t.name).concat(t.rang[e])})).join("/"):""},goToTopicDetail:function(t){t&&this.jump.appAndMiniJump(1,"/packageC/pages/topic-detail/topic-detail?id=".concat(t),this.$vhFrom)},getAvatarSrc:function(t){return t.userinfo&&t.userinfo.avatar_image?t.userinfo.avatar_image.startsWith("http")?t.userinfo.avatar_image:this.ossPrefix+t.userinfo.avatar_image:""},getUserNickname:function(t){return t.userinfo&&t.userinfo.nickname||"未知用户"},getUserType:function(t){return t.userinfo?t.userinfo.type:""},getUserLevel:function(t){return t.userinfo&&t.userinfo.user_level||0},getIsAttention:function(t){return t.is_attention},goToDetail:function(t,e){var n=this.localList[e].id,i=this.localList[e].source;this.jump.appAndMiniJump(1,"/packageC/pages/wine-comment-detail/wine-comment-detail?id=".concat(n,"&source=").concat(i),this.$vhFrom)},getPostsOtherData:function(t){var e=this;return(0,r.default)((0,a.default)().mark((function n(){var i,r,c;return(0,a.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(t&&0!==t.length){n.next=2;break}return n.abrupt("return");case 2:if(n.prev=2,i={data:t.map((function(t){return{id:t.id,source:t.source||(t.wine_data?6:2)}})),start_index:0,end_index:0},i.data[0].id){n.next=6;break}return n.abrupt("return");case 6:return n.next=8,e.$u.api.getOtherData(i);case 8:r=n.sent,0===r.error_code&&r.data.list&&(c=(0,o.default)(e.localList),r.data.list.forEach((function(t){var e=c.findIndex((function(e){return e.id===t.id&&(e.source||(e.wine_data?6:2))===t.source}));-1!==e&&(c[e]=(0,s.default)((0,s.default)({},c[e]),{},{commentnums:t.commentnums,is_digg:t.is_digg}))})),e.localList=c),n.next=15;break;case 12:n.prev=12,n.t0=n["catch"](2),console.error("获取评论数量和点赞状态失败:",n.t0);case 15:case"end":return n.stop()}}),n,null,[[2,12]])})))()},goToUserPost:function(t){t.userinfo&&t.userinfo.uid&&this.jump.appAndMiniJump(1,"/packageC/pages/my-post/my-post?uid=".concat(t.userinfo.uid),this.$vhFrom,0,!0)},formatViewNum:function(t){if(t>1e3){var e=Math.floor(t/1e3*10)/10;return e+"k+"}return t},convertModel:function(t){var e,n,i,a,o,r=t.type_data?t.type_data.split(","):[],s=r.map((function(t){return{status:"success",progress:100,error:!1,url:t,path:t,file:{path:t}}})),c=t.topic_id?t.topic_id.split(","):[],l=c.map((function(t){return parseInt(t,10)}));console.log(t.wine_data);var u=(null===(e=t.wine_data)||void 0===e||null===(n=e.taste)||void 0===n?void 0:n.map((function(t){return{value:t.value,name:t.name}})))||[];return{id:t.id,orderNo:t.main_order_no,grade:(null===(i=t.wine_data)||void 0===i?void 0:i.grade)||0,wineColor:(null===(a=t.wine_data)||void 0===a?void 0:a.wine_color)||"",wineColorImage:(null===(o=t.wine_data)||void 0===o?void 0:o.wine_color_image)||"",fragrance:t.smell_aroma||"",content:t.content||"",images:s,topics:l,taste:u}},openPostOptions:function(t){this.currentPost=t,this.showPostOpt=!0},handleEditPost:function(){if(this.currentPost)if(this.showPostOpt=!1,6===this.currentPost.source)this.jump.appAndMiniJump(1,"".concat(this.routeTable.pCWineCommentSend,"?editData=").concat(encodeURIComponent(JSON.stringify(this.convertModel(this.currentPost))),"&fromDetail=true"),this.$vhFrom,0,!0);else{var t=encodeURIComponent(JSON.stringify(this.currentPost));this.jump.appAndMiniJump(1,"".concat(this.routeTable.pCSendPost,"?postData=").concat(t),this.$vhFrom,0,!0)}},handleDeletePost:function(){var t=this;return(0,r.default)((0,a.default)().mark((function e(){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.currentPost){e.next=2;break}return e.abrupt("return");case 2:t.showPostOpt=!1,uni.showModal({title:"提示",content:"确定要删除这条帖子吗？",success:function(){var e=(0,r.default)((0,a.default)().mark((function e(n){var i,o;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!n.confirm){e.next=11;break}return e.prev=1,i={id:t.currentPost.id,type:3},e.next=5,t.$u.api.deletePost(i);case 5:o=e.sent,0===o.error_code&&(t.localList=t.localList.filter((function(e){return e.id!==t.currentPost.id})),t.$emit("refresh")),e.next=11;break;case 9:e.prev=9,e.t0=e["catch"](1);case 11:t.showPostOpt=!1;case 12:case"end":return e.stop()}}),e,null,[[1,9]])})));return function(t){return e.apply(this,arguments)}}()});case 4:case"end":return e.stop()}}),e)})))()},viewReason:function(){this.showPostOpt=!1,uni.showModal({title:"驳回原因",content:this.currentPost.reject_reason||"暂无驳回原因",showCancel:!1,confirmText:"我知道了"})}},watch:{list:{handler:function(t){var e=this;t&&t.length>0&&(this.localList=JSON.parse(JSON.stringify(t)),this.$nextTick((function(){e.getPostsOtherData(e.localList)})))},immediate:!0}}};e.default=l},"8e1d":function(t,e,n){"use strict";n.r(e);var i=n("9476"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},"913e":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.show?n("v-uni-view",{staticClass:"u-badge",class:[t.isDot?"u-badge-dot":"","mini"==t.size?"u-badge-mini":"",t.type?"u-badge--bg--"+t.type:""],style:[{top:t.offset[0]+"rpx",right:t.offset[1]+"rpx",fontSize:t.fontSize+"rpx",position:t.absolute?"absolute":"static",color:t.color,backgroundColor:t.bgColor},t.boxStyle]},[t._v(t._s(t.showText))]):t._e()},a=[]},9476:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3"),n("c975"),n("d3b7"),n("ac1f");var i={name:"u-button",props:{hairLine:{type:Boolean,default:!0},type:{type:String,default:"default"},size:{type:String,default:"default"},shape:{type:String,default:"square"},plain:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},openType:{type:String,default:""},formType:{type:String,default:""},appParameter:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},lang:{type:String,default:"en"},sessionFrom:{type:String,default:""},sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""},showMessageCard:{type:Boolean,default:!1},hoverBgColor:{type:String,default:""},rippleBgColor:{type:String,default:""},ripple:{type:Boolean,default:!1},hoverClass:{type:String,default:""},customStyle:{type:Object,default:function(){return{}}},dataName:{type:String,default:""},throttleTime:{type:[String,Number],default:1e3},hoverStartTime:{type:[String,Number],default:20},hoverStayTime:{type:[String,Number],default:150}},computed:{getHoverClass:function(){if(this.loading||this.disabled||this.ripple||this.hoverClass)return"";var t;return t=this.plain?"u-"+this.type+"-plain-hover":"u-"+this.type+"-hover",t},showHairLineBorder:function(){return["primary","success","error","warning"].indexOf(this.type)>=0&&!this.plain?"":"u-hairline-border"}},data:function(){return{rippleTop:0,rippleLeft:0,fields:{},waveActive:!1}},methods:{click:function(t){var e=this;this.$u.throttle((function(){!0!==e.loading&&!0!==e.disabled&&(e.ripple&&(e.waveActive=!1,e.$nextTick((function(){this.getWaveQuery(t)}))),e.$emit("click",t))}),this.throttleTime)},getWaveQuery:function(t){var e=this;this.getElQuery().then((function(n){var i=n[0];if(i.width&&i.width&&(i.targetWidth=i.height>i.width?i.height:i.width,i.targetWidth)){e.fields=i;var a,o;a=t.touches[0].clientX,o=t.touches[0].clientY,e.rippleTop=o-i.top-i.targetWidth/2,e.rippleLeft=a-i.left-i.targetWidth/2,e.$nextTick((function(){e.waveActive=!0}))}}))},getElQuery:function(){var t=this;return new Promise((function(e){var n="";n=uni.createSelectorQuery().in(t),n.select(".u-btn").boundingClientRect(),n.exec((function(t){e(t)}))}))},getphonenumber:function(t){this.$emit("getphonenumber",t)},getuserinfo:function(t){this.$emit("getuserinfo",t)},error:function(t){this.$emit("error",t)},opensetting:function(t){this.$emit("opensetting",t)},launchapp:function(t){this.$emit("launchapp",t)}}};e.default=i},"94c5":function(t,e,n){"use strict";n.r(e);var i=n("e5df"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},"95b6":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={uIcon:n("e5e1").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{},[n("v-uni-view",{staticClass:"u-navbar",class:{"u-navbar-fixed":t.isFixed,"u-border-bottom":t.borderBottom},style:[t.navbarStyle]},[n("v-uni-view",{staticClass:"u-status-bar",style:{height:t.statusBarHeight+"px"}}),n("v-uni-view",{staticClass:"u-navbar-inner",style:[t.navbarInnerStyle]},[t.isBack?n("v-uni-view",{staticClass:"u-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"u-icon-wrap"},[n("u-icon",{attrs:{name:t.backIconName,color:t.backIconColor,size:t.backIconSize}})],1),t.backText?n("v-uni-view",{staticClass:"u-icon-wrap u-back-text u-line-1",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()],1):t._e(),t.title?n("v-uni-view",{staticClass:"u-navbar-content-title",style:[t.titleStyle]},[n("v-uni-view",{staticClass:"u-title u-line-1",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),n("v-uni-view",{staticClass:"u-slot-content"},[t._t("default")],2),n("v-uni-view",{staticClass:"u-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?n("v-uni-view",{staticClass:"u-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+t.statusBarHeight+"px"}}):t._e()],1)},o=[]},"99c6":function(t,e,n){"use strict";n.r(e);var i=n("814f"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},"9f2b":function(t,e,n){var i=n("6188");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("63681144",i,!0,{sourceMap:!1,shadowMode:!1})},a016:function(t,e,n){"use strict";n.r(e);var i=n("8d94"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},a58c:function(t,e,n){"use strict";n.r(e);var i=n("55c2"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},a61f:function(t,e,n){"use strict";n.r(e);var i=n("8d14"),a=n("eadf");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"16d9be61",null,!1,i["a"],void 0);e["default"]=s.exports},a6e5:function(t,e,n){var i=n("66f2");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("77638b8e",i,!0,{sourceMap:!1,shadowMode:!1})},a9e0:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)},n("a4d3"),n("e01a"),n("d3b7"),n("d28b"),n("3ca3"),n("ddb0"),n("a630")},aab3:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-btn[data-v-4ed92bb2]::after{border:none}.u-btn[data-v-4ed92bb2]{position:relative;border:0;display:inline-flex;overflow:visible;line-height:1;display:flex;flex-direction:row;align-items:center;justify-content:center;cursor:pointer;padding:0 %?40?%;z-index:1;box-sizing:border-box;transition:all .15s}.u-btn--bold-border[data-v-4ed92bb2]{border:1px solid #fff}.u-btn--default[data-v-4ed92bb2]{color:#606266;border-color:#c0c4cc;background-color:#fff}.u-btn--primary[data-v-4ed92bb2]{color:#fff;border-color:#2979ff;background-color:#2979ff}.u-btn--success[data-v-4ed92bb2]{color:#fff;border-color:#19be6b;background-color:#19be6b}.u-btn--error[data-v-4ed92bb2]{color:#fff;border-color:#fa3534;background-color:#fa3534}.u-btn--warning[data-v-4ed92bb2]{color:#fff;border-color:#f90;background-color:#f90}.u-btn--default--disabled[data-v-4ed92bb2]{color:#fff;border-color:#e4e7ed;background-color:#fff}.u-btn--primary--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#a0cfff!important;background-color:#a0cfff!important}.u-btn--success--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#71d5a1!important;background-color:#71d5a1!important}.u-btn--error--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fab6b6!important;background-color:#fab6b6!important}.u-btn--warning--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fcbd71!important;background-color:#fcbd71!important}.u-btn--primary--plain[data-v-4ed92bb2]{color:#2979ff!important;border-color:#a0cfff!important;background-color:#ecf5ff!important}.u-btn--success--plain[data-v-4ed92bb2]{color:#19be6b!important;border-color:#71d5a1!important;background-color:#dbf1e1!important}.u-btn--error--plain[data-v-4ed92bb2]{color:#fa3534!important;border-color:#fab6b6!important;background-color:#fef0f0!important}.u-btn--warning--plain[data-v-4ed92bb2]{color:#f90!important;border-color:#fcbd71!important;background-color:#fdf6ec!important}.u-hairline-border[data-v-4ed92bb2]:after{content:" ";position:absolute;pointer-events:none;box-sizing:border-box;-webkit-transform-origin:0 0;transform-origin:0 0;left:0;top:0;width:199.8%;height:199.7%;-webkit-transform:scale(.5);transform:scale(.5);border:1px solid currentColor;z-index:1}.u-wave-ripple[data-v-4ed92bb2]{z-index:0;position:absolute;border-radius:100%;background-clip:padding-box;pointer-events:none;-webkit-user-select:none;user-select:none;-webkit-transform:scale(0);transform:scale(0);opacity:1;-webkit-transform-origin:center;transform-origin:center}.u-wave-ripple.u-wave-active[data-v-4ed92bb2]{opacity:0;-webkit-transform:scale(2);transform:scale(2);transition:opacity 1s linear,-webkit-transform .4s linear;transition:opacity 1s linear,transform .4s linear;transition:opacity 1s linear,transform .4s linear,-webkit-transform .4s linear}.u-round-circle[data-v-4ed92bb2]{border-radius:%?100?%}.u-round-circle[data-v-4ed92bb2]::after{border-radius:%?100?%}.u-loading[data-v-4ed92bb2]::after{background-color:hsla(0,0%,100%,.35)}.u-size-default[data-v-4ed92bb2]{font-size:%?30?%;height:%?80?%;line-height:%?80?%}.u-size-medium[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?26?%;height:%?70?%;line-height:%?70?%;padding:0 %?80?%}.u-size-mini[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?22?%;padding-top:1px;height:%?50?%;line-height:%?50?%;padding:0 %?20?%}.u-primary-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#2b85e4!important}.u-default-plain-hover[data-v-4ed92bb2]{color:#2b85e4!important;background:#ecf5ff!important}.u-success-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#18b566!important}.u-warning-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#f29100!important}.u-error-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#dd6161!important}.u-info-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#82848a!important}.u-default-hover[data-v-4ed92bb2]{color:#2b85e4!important;border-color:#2b85e4!important;background-color:#ecf5ff!important}.u-primary-hover[data-v-4ed92bb2]{background:#2b85e4!important;color:#fff}.u-success-hover[data-v-4ed92bb2]{background:#18b566!important;color:#fff}.u-info-hover[data-v-4ed92bb2]{background:#82848a!important;color:#fff}.u-warning-hover[data-v-4ed92bb2]{background:#f29100!important;color:#fff}.u-error-hover[data-v-4ed92bb2]{background:#dd6161!important;color:#fff}',""]),t.exports=e},b10b:function(t,e,n){"use strict";var i=n("6bdb"),a=n.n(i);a.a},b14f:function(t,e,n){"use strict";n.r(e);var i=n("7a86"),a=n("99c6");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("8537");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"38852b38",null,!1,i["a"],void 0);e["default"]=s.exports},bafd:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={uIcon:n("e5e1").default,uPopup:n("c4b0").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",[t._l(t.localList,(function(e,i){return n("v-uni-view",{key:i,staticClass:"bg-ffffff b-rad-20 mt-20 ml-24 mr-24 pt-28 pr-22 pb-28 pl-22 p-rela",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.goToDetail(e,i)}}},[n("v-uni-view",{staticClass:"d-flex"},[n("v-uni-view",{staticClass:"p-rela w-88 h-88",on:{click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.goToUserPost(e)}}},[n("v-uni-image",{staticClass:"w-88 h-88 b-rad-p50",attrs:{src:t.getAvatarSrc(e),mode:"aspectFill"}}),e.userinfo&&e.userinfo.certified_info?n("v-uni-image",{staticClass:"p-abso bottom-n-02 right-0 w-24 h-26",attrs:{src:t.ossIcon("/comm/certified_24_26.png")}}):t._e()],1),n("v-uni-view",{staticClass:"ml-10 flex-sb-c w-p100"},[n("v-uni-view",{staticClass:"d-flex a-center"},[n("v-uni-text",{staticClass:"font-28 font-wei text-2d2d2d l-h-40"},[t._v(t._s(t.getUserNickname(e)))]),2!=t.getUserType(e)?n("v-uni-view",{staticClass:"font-22 d-flex j-center a-center b-rad-26 text-ffffff ml-10 w-80 h-34 bg-ff9300"},[t._v("LV."+t._s(t.getUserLevel(e)))]):n("v-uni-view",{staticClass:"font-22 d-flex j-center a-center b-rad-10 text-ffffff ml-10 w-80 h-34 bg-e80404"},[t._v("官方")])],1),n("v-uni-view",{staticClass:"d-flex a-center"},[t.myPostSelf&&2===e.status?n("v-uni-view",{staticClass:"reject-icon-post"},[t._v("未通过")]):t._e(),t.myPostSelf?n("v-uni-view",{staticClass:"ml-20",on:{click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.openPostOptions(e)}}},[n("u-icon",{attrs:{name:"more-dot-fill",color:"#666",size:"34"}})],1):t._e(),!t.isFollowList&&e.userinfo&&e.userinfo.uid!=t.currentUid?n("v-uni-view",{staticClass:"w-100 mt-06 ptb-04-plr-00 font-24 text-center text-9 l-h-34 b-rad-26",class:[e.is_attention?"text-3 b-s-02-d8d8d8":"text-2e7bff bg-e2ebfa"],on:{click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.handleFollow(e,i)}}},[t._v(t._s(e.is_attention?"已关注":"+关注"))]):t._e()],1)],1)],1),e.topics&&e.topics.length?n("v-uni-view",{staticClass:"mt-24"},[n("v-uni-view",{staticClass:"topic-container"},t._l(e.topics,(function(e,i){return n("v-uni-text",{key:i,staticClass:"bg-e2ebfa ptb-02-plr-22 b-rad-26 font-24 text-2e7bff l-h-34 mr-10 mb-10 topic-item",on:{click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.goToTopicDetail(e.id)}}},[t._v("#"+t._s(e.title)+"#")])})),1)],1):t._e(),n("v-uni-view",{staticClass:"font-24 text-3 l-h-40 o-hid text-hidden-4 d-flex a-center"},[e.is_best?n("v-uni-image",{staticClass:"w-44 h-30 mr-06",staticStyle:{"vertical-align":"middle"},attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/best-tag.png"}}):t._e(),e.is_top?n("v-uni-image",{staticClass:"w-44 h-30 mr-06",staticStyle:{"vertical-align":"middle"},attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/top-tag.png"}}):t._e(),n("v-uni-text",[t._v(t._s(e.content))])],1),e.video_url?n("v-uni-view",{staticClass:"mt-20"},[n("v-uni-video",{staticClass:"w-p100",attrs:{src:e.video_url,poster:e.cover_img}})],1):t._e(),6===e.source?n("v-uni-view",[n("v-uni-view",{staticClass:"mt-10 mb-20 d-flex a-center",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.goToWineDetail(i)}}},[n("v-uni-image",{staticClass:"w-20 h-20 flex-shrink-0",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/wine_link.png"}}),n("v-uni-view",{staticClass:"ml-10 wine-data-title"},[t._v(t._s(e.wine_data.period_title))])],1),1===e.wine_data.comment_type?n("v-uni-view",{staticClass:"bg-f7f7f7 w-p100 p-20 b-rad-06"},[e.wine_data&&e.wine_data.grade?n("v-uni-view",{staticClass:"d-flex a-center j-sb"},[n("v-uni-view",{staticClass:"d-flex a-center"},[n("v-uni-image",{staticClass:"w-34 h-34 mr-10",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/wine-point.png"}}),n("v-uni-view",{staticClass:"wine-data-bottom-title"},[t._v("评分")])],1),n("v-uni-view",{staticClass:"d-flex a-center"},[t._l(t.getStarArray(e),(function(t){return n("v-uni-image",{key:t.index,staticClass:"w-26 h-26 mr-6",attrs:{src:t.active?"https://images.vinehoo.com/vinehoomini/v3/comm/point-active.png":"https://images.vinehoo.com/vinehoomini/v3/comm/point.png"}})})),n("v-uni-view",{staticClass:"ml-10 wine-value"},[t._v(t._s(e.wine_data.grade)+"分")])],2)],1):t._e(),e.wine_data&&e.wine_data.taste&&e.wine_data.taste.length?n("v-uni-view",{staticClass:"mt-10 d-flex a-center j-sb"},[n("v-uni-view",{staticClass:"d-flex a-center"},[n("v-uni-image",{staticClass:"w-34 h-34 mr-10",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/wine-type.png"}}),n("v-uni-view",{staticClass:"wine-data-bottom-title"},[t._v("品味")])],1),n("v-uni-view",{staticClass:"wine-value text-ellipsis"},[t._v(t._s(t.getFormatTaste(e)))])],1):t._e()],1):t._e()],1):t._e(),e.type_data?n("v-uni-view",{staticClass:"d-flex flex-wrap mt-20"},t._l(t.getImageArray(e.type_data).slice(0,3),(function(i,a){return n("v-uni-view",{key:a,staticClass:"image-wrapper p-rela",class:[1===t.getImageArray(e.type_data).length?"w-330":"w-210 h-210"],style:{marginRight:2===a?"0":"14rpx"},on:{click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.previewImage(t.getImageArray(e.type_data),a)}}},[n("v-uni-image",{staticClass:"b-rad-10 w-p100 h-p100",attrs:{src:i,mode:1===t.getImageArray(e.type_data).length?"widthFix":"aspectFill"}}),2===a&&t.getImageArray(e.type_data).length>3?n("v-uni-view",{staticClass:"image-overlay d-flex j-center a-center"},[n("v-uni-text",{staticClass:"remaining-count"},[t._v("+"+t._s(t.getImageArray(e.type_data).length-3))])],1):t._e()],1)})),1):t._e(),n("v-uni-view",{staticClass:"d-flex j-sa a-center mt-24"},[n("v-uni-view",{staticClass:"d-flex a-center"},[n("v-uni-image",{staticClass:"w-26 h-26",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/view.png",mode:"aspectFill"}}),n("v-uni-text",{staticClass:"ml-06 font-24 text-6"},[t._v(t._s(t.formatViewNum(e.viewnums||0)))])],1),n("v-uni-view",{staticClass:"d-flex a-center"},[n("v-uni-image",{staticClass:"w-26 h-26",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/comm.png",mode:"aspectFill"}}),n("v-uni-text",{staticClass:"ml-06 font-24 text-6"},[t._v(t._s(e.commentnums||0))])],1),n("v-uni-view",{staticClass:"d-flex a-center",on:{click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.handleLike(e)}}},[n("v-uni-image",{staticClass:"w-26 h-26",attrs:{src:e.is_digg?"https://images.vinehoo.com/vinehoomini/v3/comm/zan_active.png":"https://images.vinehoo.com/vinehoomini/v3/comm/zan_none.png",mode:"aspectFill"}}),n("v-uni-text",{staticClass:"ml-06 font-24",class:e.is_digg?"text-e80404":"text-6"},[t._v(t._s(e.diggnums||0))])],1)],1)],1)})),n("u-popup",{attrs:{mode:"bottom","border-radius":20,"mask-custom-style":{background:"rgba(0, 0, 0, 0.3)"}},model:{value:t.showPostOpt,callback:function(e){t.showPostOpt=e},expression:"showPostOpt"}},[n("v-uni-view",{staticClass:"pl-32 pr-32"},[t.currentPost&&2===t.currentPost.status?n("v-uni-view",{staticClass:"bb-s-01-dedede pt-36 pb-36 text-center font-32 text-3",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.viewReason.apply(void 0,arguments)}}},[t._v("查看原因")]):t._e(),!t.currentPost||6!==t.currentPost.source&&2!==t.currentPost.status?t._e():n("v-uni-view",{staticClass:"bb-s-01-dedede pt-36 pb-36 text-center font-32 text-3",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.handleEditPost.apply(void 0,arguments)}}},[t._v("重新编辑")]),t.currentPost&&!t.currentPost.source?n("v-uni-view",{staticClass:"bb-s-01-dedede pt-36 pb-36 text-center font-32 text-3",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.handleDeletePost.apply(void 0,arguments)}}},[t._v("删除帖子")]):t._e(),n("v-uni-view",{staticClass:"pt-36 pb-36 text-center font-32 text-9",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showPostOpt=!1}}},[t._v("取消")])],1)],1)],2)},o=[]},cfe0:function(t,e,n){"use strict";var i=n("03d4"),a=n.n(i);a.a},d0ff:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,i.default)(t)||(0,a.default)(t)||(0,o.default)(t)||(0,r.default)()};var i=s(n("4053")),a=s(n("a9e0")),o=s(n("dde1")),r=s(n("10eb"));function s(t){return t&&t.__esModule?t:{default:t}}},e5df:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var i={name:"u-badge",props:{type:{type:String,default:"error"},size:{type:String,default:"default"},isDot:{type:Boolean,default:!1},count:{type:[Number,String]},overflowCount:{type:Number,default:99},showZero:{type:Boolean,default:!1},offset:{type:Array,default:function(){return[20,20]}},absolute:{type:Boolean,default:!0},fontSize:{type:[String,Number],default:"24"},color:{type:String,default:"#ffffff"},bgColor:{type:String,default:""},isCenter:{type:Boolean,default:!1}},computed:{boxStyle:function(){var t={};return this.isCenter?(t.top=0,t.right=0,t.transform="translateY(-50%) translateX(50%)"):(t.top=this.offset[0]+"rpx",t.right=this.offset[1]+"rpx",t.transform="translateY(0) translateX(0)"),"mini"==this.size&&(t.transform=t.transform+" scale(0.8)"),t},showText:function(){return this.isDot?"":this.count>this.overflowCount?"".concat(this.overflowCount,"+"):this.count},show:function(){return 0!=this.count||0!=this.showZero}}};e.default=i},e688:function(t,e,n){"use strict";var i=n("9f2b"),a=n.n(i);a.a},eadf:function(t,e,n){"use strict";n.r(e);var i=n("1589"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},ec3e:function(t,e,n){"use strict";n.r(e);var i=n("bafd"),a=n("a016");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("e688");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"61dc3b9d",null,!1,i["a"],void 0);e["default"]=s.exports},fa94:function(t,e,n){"use strict";var i=n("062a"),a=n.n(i);a.a}}]);