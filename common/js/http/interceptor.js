import { generateSign } from 'vinehoo-v3-api-sign' //验签
import guid from '@/common/js/fun/guid.js'

const showErrorModal = (content) => {
  uni.showModal({ title: '提示', content, showCancel: false, confirmText: '确定' })
}

// 封装http请求、图片地址拦截
const install = (Vue, vm) => {
  // 根域名
  let baseUrl = vm.$store.state.requestPrefix //v3请求普通接口的根域名
  const ossBaseUrl = vm.$store.state.ossPrefix //v3请求json文件的根域名

  // #ifdef H5
  baseUrl = ''
  // #endif

  // 自定义配置参数，
  Vue.prototype.$u.http.setConfig({
    baseUrl, //测试链接
    showLoading: false, // 是否显示请求中的loading
    loadingText: '请求超时', // 请求loading中的文字提示
    loadingTime: 15000, // 在此时间内，请求还没回来的话，就显示加载中动画，单位ms
    originalData: true, // 是否在拦截器中返回服务端的原始数据
    loadingMask: false, // 展示loading的时候，是否给一个透明的蒙层，防止触摸穿透
    // header: { 'content-type': 'application/json; charset=UTF-8' }, // 配置请求头信息
  })

  // 请求拦截部分，如配置，每次请求前都会执行
  ;(Vue.prototype.$u.http.interceptor.request = (config) => {
    // 引用token
    // 方式一，存放在vuex的token，假设使用了uView封装的vuex方式
    // 见：https://uviewui.com/components/globalVariable.html
    // config.header.token = vm.token;

    // 方式二，如果没有使用uView封装的vuex方法，那么需要使用$store.state获取
    // config.header.token = vm.$store.state.token;

    // 方式三，如果token放在了globalData，通过getApp().globalData获取
    // config.header.token = getApp().globalData.username;

    // 方式四，如果token放在了Storage本地存储中，拦截是每次请求都执行的
    // 所以哪怕您重新登录修改了Storage，下一次的请求将会是最新值
    try {
      const loginInfo = uni.getStorageSync('loginInfo') || '' //判断有无用户登录信息
      const uniqueId = uni.getStorageSync('uniqueId') || '' //判断有无用全局唯一id
      const source = uni.getStorageSync('source') || '' //来源标识
      if (config.method == 'GET') {
        config.params = config.data
      } // 如果是get请求强制将data值赋值给params
      // if(config.method == 'POST') { config.header = { 'content-type': 'application/x-www-form-urlencoded' }} //如果是POST请求就定义content-type为post提交格式
      if (JSON.stringify(config.data) == '{}') {
        config.data = undefined
      } // 如果不传data值默认为 undefined
      if (JSON.stringify(config.params) == '{}') {
        config.params = undefined
      } // 如果不传params值默认为 undefined
      if (config?.data?.isJson) {
        config.url = ossBaseUrl + config.url
      } // 判断用户是否请求json文件
      // 记录哪个端
      let client = ''
      if (vm.$store.state.from == '1') client = 'android' //安卓
      else if (vm.$store.state.from == '2') client = 'ios' //ios
      else if (vm.$store.state.from == 'next') client = 'harmonyos' //鸿蒙
      else client = 'h5' //小程序或者h5（uni项目暂时没有分h5和小程序、暂时使用小程序）

      const uid = loginInfo.uid || uniqueId || guid()

      if (config.url === '/api/maidian/v3/report/report') {
        let clientNum = 3
        if (vm.$store.state.from == '1') clientNum = 1
        else if (vm.$store.state.from == '2') clientNum = 0
        else if (vm.$store.state.from == 'next') clientNum = 5
        config.data.data = config.data.data.map((item) => ({
          ...item,
          uid: `${uid}`,
          is_login: loginInfo.token ? 1 : 0,
          client: clientNum,
          created_time: Math.round(Date.now() / 1000),
        }))
      }

      const isTestIdGoodsDetails =
        window.location.search === '?id=100020' && window.location.pathname === '/pages/goods-detail/goods-detail'
      config.header = {
        //设置请求头
        ...generateSign(config, loginInfo.token || ''),
        ...config.header,
        'vinehoo-client': client,
        'vinehoo-client-version': vm.$store.state.version,
        'access-token': loginInfo.token || '',
        'vinehoo-uid': isTestIdGoodsDetails ? 'vinehoo-monitor' : uid,
      }
      if (loginInfo.uid) {
        config.header.guest_uid = uniqueId
      }
      window.header = config.header

      const { vhPlatform, vhEvent } = config?.data || {}
      if (config.url === '/api/orders/v3/order/create' && config.data.oneSource) {
        const { oneSourcePlatform, oneSourceEvent, oneSourceUser } = config.data.oneSource
        config.header['source-platform'] = oneSourcePlatform
        config.header['source-event'] = oneSourceEvent
        if (oneSourceUser) config.header['source-user'] = oneSourceUser
      } else if (source.sourcePlatform && source.sourceEvent) {
        const { sourcePlatform, sourceEvent, sourceUser, timeStamp, sourceTimeliness } = source
        if (timeStamp + sourceTimeliness * 60 * 1000 > Date.now()) {
          if (sourceUser) config.header['source-user'] = sourceUser
          config.header['source-platform'] = sourcePlatform
          config.header['source-event'] = sourceEvent
        } else {
          uni.removeStorageSync('source') //活动标识已失效需要清除来源标识
        }
      } else if (vhPlatform && vhEvent) {
        config.header['source-platform'] = vhPlatform
        config.header['source-event'] = vhEvent
      }
      return config
    } catch (e) {}
  }),
    // 响应拦截，如配置，每次请求结束都会执行本方法
    (Vue.prototype.$u.http.interceptor.response = (res) => {
      // 获取服务器当前时间
      // vm.$store.commit('muServercurrentTime', res.header.Date)

      // 安卓获取接口返回信息
      if (['1', '2'].includes(vm.$store.state.from)) {
        wineYunJsBridge.openAppPage({
          client_path: { android_path: 'log', ios_path: 'log' },
          ad_path_param: [{ android_val: JSON.stringify(res), ios_val: JSON.stringify(res) }],
        })
      }
      // 判断是否请求接口成功（请求服务器状态码 = 200 代表成功 其它详见不同状态码）
      switch (res.statusCode) {
        case 200:
          // 接口内部状态码（ 0 = 接口数据请求正常 其它状态直接返回服务器错误提示信息）
          if (res.data.error_code == 0) {
            return res.data
          } else if (res.data.error_code == 401) {
            try {
              uni.clearStorageSync()
              if (location && ['/', '/pages/index/index'].includes(location.pathname)) return
              //   vm.feedback.toast({ title: res.data.error_msg })
              setTimeout(() => {
                if (vm.$store.state.from == '1' || vm.$store.state.from == '2' || vm.$store.state.from == 'next') {
                  wineYunJsBridge.openAppPage({
                    client_path: { ios_path: 'login', android_path: 'login' },
                  })
                } else {
                  uni.setStorageSync('uniqueId', vm.$u.guid()) //先存下uniqueId（全局唯一标识）
                  if (vm.$store.state.from == '3') {
                    vm.jump.reLaunch(vm.pages.getPrvePageFullPath()) //如果是pc端预览商品详情页出现登录失效的情况，重新刷新一下页面
                  } else {
                    vm.$u.throttle(() => {
                      vm.jump.navigateTo('/pages/login/login?isTokenFail=1')
                    }, 500)
                    // #ifdef H5
                    document.cookie = 'h5token=; path=/; domain=vinehoo.com; expires=Thu, 01-Jan-1970 00:00:01 GMT'
                    document.cookie = 'h5uid=; path=/; domain=vinehoo.com; expires=Thu, 01-Jan-1970 00:00:01 GMT'
                    // #endif
                  }
                }
              }, 500)
            } catch (e) {
              // error
            }
            return false
          } else {
            if (res.data && res.data.error_msg && !res.header.req_url.includes('/maidian/v3/report/h5Report')) {
              vm.feedback.toast({ title: res.data.error_msg })
            }
            if (!res.header.req_url.includes('/maidian/v3/report/h5Report') && (!res.data.error_msg || !res.data)) {
              // 上报错误接口
              vm.$u.http.post('/api/maidian/v3/report/h5Report', {
                url: res.header.req_url,
                error_info: res.data,
              })
            }
            return false
          }
        case 401:
          vm.feedback.toast({ title: '401 用户登录失效' })
          return false
        case 404:
          showErrorModal('404 找不到接口地址')
          // vm.feedback.toast({title: '404 找不到接口地址'})
          return false
        case 500:
          showErrorModal('服务器繁忙')
          // vm.feedback.toast({title: '服务器繁忙'})
          return false
        default:
          console.log('-------------------------aaa', res)
          console.log(res)
          // if (res.errMsg.includes('request:fail')) {
          //   showErrorModal('数据读取失败，请检查网络环境！')
          //   // vm.feedback.toast({ title: '数据读取失败，请检查网络环境！'})
          // } else {
          //   showErrorModal(JSON.stringify(res))
          //   // vm.feedback.toast({title: JSON.stringify(res)})
          // }
          return false
      }
    })
}

// 导出方法
export default { install }
