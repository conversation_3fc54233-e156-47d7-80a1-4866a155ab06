// Api集中管理

import { data } from 'uview-ui/libs/mixin/mixin'

// 此处第二个参数vm，就是我们在页面使用的this，你可以通过vm获取vuex等操作，更多内容详见uView对拦截器的介绍部分：
const install = (Vue, vm) => {
  const getVersionInfo = () => vm.$u.get('/api/user/v3/client/getVersionInfo')
  // 登录板块
  let quickLogin = (data) => vm.$u.post('/api/user/v3/wechat/quickLogin', data) //微信快捷登录
  const sendSmsCode = (params) => vm.$u.post('/api/user/v3/login/sendSmsCode', params) //发送验证码
  const loginByCode = (params) => vm.$u.post('/api/user/v3/login/loginByCode', params) //登录（验证码方式）

  // 首页板块
  let indexCombine = (params) => vm.$u.get('/api/openapi/v3/combine/home', params) //首页聚合接口
  let newsDetail = (params) => vm.$u.get('/api/marketing-conf/v3/bullein/clientdetail', params) //首页快报详情
  let liveBroadcastReservation = (data) => vm.$u.post('/api/live/v3/live/subscribe', data) //直播预约
  let hoverButtonInfo = (params) => vm.$u.get('/api/marketing-conf/v3/hoverButton', params) //首页悬浮按钮

  // 我的板块
  let userData = (params) => vm.$u.get('/api/user/v3/profile/getUserData', params) //获取用户数据
  let signIn = (data) => vm.$u.post('/api/user/v3/user/sendSignIn', data) //用户签到
  let mineFuncList = (params) => vm.$u.get('/api/marketing-conf/v3/label/clientlist', params) //个人中心功能列表
  let userSpecifiedData = (params) => vm.$u.get('/api/user/v3/profile/getSpecifiedData', params) //用户指定数据
  let setNewsPush = (data) => vm.$u.post('/api/user/v3/profile/setNewsPush', data) //设置消息推送
  let agreeProtocol = (data) => vm.$u.post('/api/user/v3/user/agreeProtocol', data) //用户同意协议
  let getFootprintList = (params) => vm.$u.get('/api/user/v3/footprint/getFootprintList', params) //我的足迹列表
  let delFootprint = (data) => vm.$u.post('/api/user/v3/footprint/delFootprint', data) //删除足迹

  // 优惠券板块
  let couponList = (params) => vm.$u.get('/api/coupon/v3/couponissue/my', params) //个人中心优惠券列表
  const getCouponList = (params) => vm.$u.get('/api/coupon/v3/couponissue/my2', params)
  const getCouponListByOrder = (params) => vm.$u.post('/api/coupon/v3/couponissue/orderCoupon2', params)
  let orderCoupon = (data) => vm.$u.post('/api/coupon/v3/couponissue/orderCoupon', data) //获取订单可用优惠劵
  let couponDetail = (params) => vm.$u.get('/api/coupon/v3/coupon/detail', params) //获取优惠券详情

  // 个人信息板块
  let updateUserInfo = (data) => vm.$u.post('/api/user/v3/profile/update', data) //修改用户信息

  // 日常任务板块
  let dailyTasksList = (params) => vm.$u.get('/api/user/v3/user/getDailyTasksList', params) //日常任务列表

  // 消息中心
  let messageUnreadNum = (params) => vm.$u.get('/api/apppush/v3/push/unReadNum', params) //未读消息数量
  let messagePushList = (params) => vm.$u.get('/api/apppush/v3/push/index', params) //消息推送列表
  let messageClean = (data) => vm.$u.post('/api/apppush/v3/push/clear', data) //清空消息

  // 收货地址管理板块
  let addressList = (params) => vm.$u.get('/api/user/v3/address/list', params) //收货地址列表
  let regionList = (params) => vm.$u.get('/vinehoo/client/common/area.json', params) //地区树形列表（JSON文件）
  let addAddress = (data) => vm.$u.post('/api/user/v3/address/add', data) //添加收货地址
  let deleteAddress = (data) => vm.$u.post('/api/user/v3/address/delete', data) //删除收货地址
  let updateAddress = (data) => vm.$u.post('/api/user/v3/address/update', data) //修改收货地址
  let setDefaultAddress = (data) => vm.$u.post('/api/user/v3/address/setDefault', data) //设置默认地址
  let addressAiMatch = (params) => vm.$u.get('/api/user/v3/address/AiMatch', params) //收货地址智能匹配接口

  // 发票抬头管理板块
  let invoiceList = (params) => vm.$u.get('/api/user/v3/receipt/list', params) //发票列表
  let addInvoice = (data) => vm.$u.post('/api/user/v3/receipt/add', data) //添加发票信息
  let deleteInvoice = (data) => vm.$u.post('/api/user/v3/receipt/delete', data) //删除发票信息
  let updateInvoice = (data) => vm.$u.post('/api/user/v3/receipt/update', data) //修改发票信息
  let openInvoice = (data) => vm.$u.post('/api/invoice/v3/invoice/create', data) //开发票

  // 历史发票
  const invoiceHistoryList = (params) => vm.$u.get('/api/go-invoice/v3/vineQuery/invoiceByUser', params) //发票历史列表
  const invoiceHistoryDetail = (params) => vm.$u.get('/api/go-invoice/v3/vineQuery/invoiceHistoryByInvoiceCode', params) //发票历史详情
  const invoiceImage = (params) => vm.$u.get('/api/go-invoice/v3/query/jpgByInvoiceCode', params) //获取发票图片

  // 闪购板块
  let flashIndexGoodsList = (params) => vm.$u.get('/api/commodities/v3/periods/indexListSort', params) //闪购首页商品列表（默认进入页面时请求）
  let flashGoodsList = (data) => vm.$u.post('/api/openapi/v3/commodity/list', data) //闪购筛选商品列表（筛选时调取 闪购：0、秒发：1、跨境：2、尾货：3 ）
  let flashChannelList = (params) => vm.$u.get('/api/fullgift/v3/api/FlashSaleClassList', params) //闪购首页频道列表
  // let flashChannelList = ( params ) => vm.$u.get('/vinehoo/client/commodity/list/channel_list.json', params) //闪购首页频道列表（废弃）
  let flashFilterList = (params) => vm.$u.get('/api/wiki/v3/screen/clientsglist', params) //闪购过滤列表（类型、国家、关键字）
  const getFlashLiquorFilterList = () => vm.$u.get('/api/wiki/v3/screen/clientsgljlist')
  const getFlashKjFilterList = () => vm.$u.get('/api/wiki/v3/screen/clientkjlist')
  const getFlashAggregateData = (params) => vm.$u.post('/api/go-distribution/v3/aggregate/client_tag', params)
  // 秒发板块
  let secondCombine = (params) => vm.$u.get('/api/openapi/v3/seconds/home', params) //秒发聚合接口
  let secondHairFilterList = (params) => vm.$u.get('/api/wiki/v3/screen/clientmflist', params) //秒发过滤列表（类型、国家、关键字）
  // let secondGoldAreaList = ( params ) => vm.$u.get('/api/activity/v3/second/getList', params) //秒发金刚区列表
  let secondGoldAreaList = (params) => vm.$u.get('/api/commodities/v3/second/getSecondTopFilter', params) //秒发金刚区列表
  // let secondHairSonPageSecondClassifyList = ( params ) => vm.$u.get('/api/commodities/v3/second/getSecondPeriodsFilter', params) //秒发二级页面二级筛选列表
  let secondHairSonPageSecondClassifyList = (params) =>
    vm.$u.get('/api/commodities/v3/second/getSecondLeftFilter', params) //秒发二级页面二级筛选列表
  // let secondHairSonPageList = ( params ) => vm.$u.get('/api/commodities/v3/second/getSecondES', params) //秒发二级页面列表
  let secondHairSonPageList = (params) => vm.$u.get('/api/commodities/v3/second/getSecondFilterGoods', params) //秒发二级页面列表

  // 社区板块
  let fansAndAttentionList = (params) => vm.$u.get('/api/user/v3/attention/list', params) //用户关注、粉丝列表
  let userAttenAndCncelAtten = (data) => vm.$u.post('/api/user/v3/attention/operate', data) //用户关注、取消关注

  // 商品详情板块
  let shoppingCartNum = (params) => vm.$u.get('/api/orders/v3/cart/count', params) //购物车数量
  let goodsDetailJson = (params) => vm.$u.get(`/vinehoo/client/commodities/periods/${params.id}.json`, params) //获取商品详情期数json文件
  let goodsDetail = (params) => vm.$u.get('/api/commodities/v3/period/getPeriodJson', params) //获取商品详情期数文件(弃用)
  let groupInfo = (params) => vm.$u.get('/api/orders/v3/order/getGroupInfo', params) //拼团信息
  // let packageDetail = ( params ) => vm.$u.get('/api/openapi/v3/package/detail', params) //查询套餐详情（黎培阳（网关））
  let packageDetail = (params) => vm.$u.get('/api/commodities/v3/package/getPeriodsPackageInventory', params) //查询套餐详情（商品）
  let packageDetailSeckill = (params) => vm.$u.get('/api/seckill/v3/goods/inventoryByPeriod?period=80174', params) //查询套餐详情（商品） 秒杀
  let vmallPackageDetail = (params) => vm.$u.get('/api/commodities/v3/package/getVmallPeriodsPackageInventory', params) //查询商家套餐详情（商品）
  let secondPackageDetail = (params) =>
    vm.$u.get('/api/commodities/v3/package/getPeriodsSecondPackageInventory', params) //查询秒发套餐详情（商品）
  let goodsCollectionStatus = (params) => vm.$u.get('/api/commodities/v3/periods/existsCollection', params) //商品收藏状态
  let goodsCollection = (data) => vm.$u.post('/api/commodities/v3/periods/collection', data) //商品收藏
  let goodsDiscountArea = (params) => vm.$u.get('/api/ordersPrice/v3/getOrderCoupon', params) //商品详情优惠区
  let goodsCommentAdd = (data) => vm.$u.post('/api/commodities/v3/periods/createComment', data) //添加商品评论
  let goodsCommentList = (params) => vm.$u.get('/api/commodities/v3/periods/commentList', params) //商品评论列表
  const getCommentUserStatus = (params) => vm.$u.post('/api/commodities/v3/periods/getCommentUserStatus', params) //商品评论列表用户相关状态
  let goodsCollectionList = (params) => vm.$u.get('/api/commodities/v3/periods/collectionList', params) //商品收藏列表
  let goodsCommentThumbsUp = (data) => vm.$u.post('/api/commodities/v3/comment/like', data) //商品评论点赞
  let deleteGoodsCollection = (data) => vm.$u.post('/api/commodities/v3/periods/delCollection', data) //删除商品收藏
  let goodsCouponInfo = (data) => vm.$u.post('/api/coupon/v3/couponissue/getPeriodsCoupon', data) //商品优惠券信息

  // 购物车板块
  let addShoppingCart = (data) => vm.$u.post('/api/orders/v3/cart/addShoppingCart', data) // 加入购物车
  let shoppingCartList = (params) => vm.$u.get('/api/orders/v3/cart/shoppingCartList', params) //购物车列表
  let shoppingCartCount = (params) => vm.$u.get('/api/orders/v3/cart/count', params) //购物车数量
  let shoppingCartMoneyCalclute = (data) => vm.$u.post('/api/orders/v3/cart/calculateGoodsMoney', data) //购物车勾选金额计算
  let changeShoppingCartNums = (data) => vm.$u.post('/api/orders/v3/cart/changeNums', data) //购物车数量加减
  let shoppingCatDelete = (data) => vm.$u.post('/api/orders/v3/cart/delete', data) //删除购物车

  // 确认订单板块
  let calculateOrderMoney = (data) => vm.$u.post('/api/ordersPrice/v3/order/calculateOrderMoney', data) //订单金额计算接口
  let createOrder = (data) => vm.$u.post('/api/orders/v3/order/create', data) //创建订单
  let orderRelaText = (params) => vm.$u.get('/api/orders/v3/additional/getOrderText', params) //获取订单相关文本接口
  let orderInvoiceDetailList = (data) => vm.$u.post('/api/orders/v3/additional/getInvoiceInfo', data) //订单发票明细列表
  let orderAddPurchase = (params) => vm.$u.get('/api/commodities/v3/ap/getAddPurchase', params) //订单加购

  // 支付板块
  let payMethod = (data) => vm.$u.post('/api/payment/v3/ums/pay', data) //银联支付
  let queryUmsPayOrderStatus = (data) => vm.$u.post('/api/payment/v3/ums/query', data) //查询银联支付订单状态

  // 我的订单列表板块
  let myOrderList = (params) => vm.$u.get('/api/orders/v3/order/personalList', params) //我的订单列表
  let cancelDeleteOrder = (data) => vm.$u.post('/api/orders/v3/order/cancelDeleteOrder', data) //用户取消、删除订单接口
  let updateOrderStatus = (data) => vm.$u.post('/api/orders/v3/order/update', data) //修改订单状态
  let orderReceipt = (data) => vm.$u.post('/api/orders/v3/order/goodsReceipt', data) //确认收货
  let urlShorten = (params) => vm.$u.get('/api/shorten', params) //确认收货

  // 订单详情板块
  let orderDetail = (params) => vm.$u.get('/api/orders/v3/order/personalDetail', params) //订单详情接口
  const queryCommonOrderDetail = (params) => vm.$u.get('/api/orders/v3/order/detail', params) //公共获取订单详情接口
  const giftCardsOrderDetail = (params) => vm.$u.get('/api/user/v3/giftCards/orderDetail', params) //公共获取订单详情接口
  // 物流板块
  let logisticsDetails = (params) => vm.$u.get('/api/logistics/mapTrack/v3/track', params) //物流详情
  let logisticsCompanyList = (params) => vm.$u.get('/api/logistics/mapTrack/v3/company', params) //物流公司列表

  // 酒会板块
  let winePartyList = (params) => vm.$u.get('/api/wineparty/v3/wineparty/webList', params) //酒会列表
  let winePartyDetail = (params) => vm.$u.get('/api/wineparty/v3/wineparty/detail', params) //酒会详情
  let winePartyOroupInfo = (params) => vm.$u.get('/api/wineparty/v3/order/getGroupInfo', params) //酒会拼团信息
  let winePartyCityList = (params) => vm.$u.get('/api/wineparty/v3/wineparty/cityList', params) //酒会城市列表
  let winePartyCalculateOrderMoney = (data) => vm.$u.post('/api/wineparty/v3/order/calculateOrderMoney', data) //酒会计算订单金额
  let winePartyCreateOrder = (data) => vm.$u.post('/api/wineparty/v3/order/create', data) //酒会创建订单
  let myWinePartyList = (params) => vm.$u.get('/api/wineparty/v3/wineparty/myWinePartyList', params) //我的酒会订单列表
  let winePartyOrderDetail = (params) => vm.$u.get('/api/wineparty/v3/order/detail', params) //酒会订单详情
  let winePartyWriteOffCode = (params) => vm.$u.get('/api/wineparty/v3/wineparty/getCancelCode', params) //酒会核销码

  // 酒闻板块
  let wineSmellList = (params) => vm.$u.get('/api/news/v3/articleClient/getArticleList', params) //酒闻列表
  let wineSmellDetail = (params) => vm.$u.get('/api/news/v3/articleClient/getArticleDetails', params) //酒闻详情
  let wineSmellDetailJson = (params) => vm.$u.get(`/vinehoo/client/news/article/${params.id}.json`, params) //获取酒闻详情期数json文件
  let wineSmellCollectionStatus = (data) => vm.$u.post('/api/news/v3/articleClient/getIsCollect', data) //酒闻获取收藏状态
  let wineSmellCommentAdd = (data) => vm.$u.post('/api/news/v3/articleClient/MakeCommentOn', data) //添加酒闻详情评论
  let wineSmellCommentList = (params) => vm.$u.get('/api/news/v3/articleClient/getArticleComment', params) //酒闻详情评论列表
  let wineSmellThumbsUp = (data) => vm.$u.post('/api/news/v3/articleClient/doDigg', data) //资讯（酒闻）\评论点赞前端
  const cancelEnjoyWineComment = (parmas) => vm.$u.post('/api/news/v3/articleClient/cancelLike', parmas)
  let wineSmellCollectinList = (params) => vm.$u.get('/api/news/v3/articleClient/getMyArticleCollectList', params) //酒闻收藏列表
  let wineSmellCollection = (data) => vm.$u.post('/api/news/v3/articleClient/getArticleCollect', data) //酒闻收藏（收藏/取消收藏）

  // 订单开票板块
  let personalOrderInvoiceList = (params) => vm.$u.get('/api/orders/v3/order/personalInvoiceOrderList', params) //个人中心订单开票列表
  let winePartyOrderInvoiceList = (params) => vm.$u.get('/api/wineparty/v3/order/invoiceOrderList', params) //酒会订单可开票列表

  // 热门推荐，猜你喜欢
  let recommendList = (params) => vm.$u.get('/api/commodities/v3/periods/recommendList', params) //热门推荐，猜你喜欢列表

  // 广告列表
  let advertisingList = (params) => vm.$u.get('/api/marketing-conf/v3/ad/clientlist', params) //广告列表（banner、开屏、弹窗、胶囊、广告位）

  // 全局搜索
  let globalSearch = (params) => vm.$u.get('/api/openapi/v3/search/aggregation', params) // 全局搜索
  let hotSearch = (params) => vm.$u.get('/api/marketing-conf/v3/hotkeyword/clientlist', params) // 热门搜索

  // 直播板块
  let ossLiveBroadcastList = (params) => vm.$u.get('/api/live/v3/live/list', params) // oss直播列表（直播源为oss）

  // 申请售后
  let applyAfterSale = (data) => vm.$u.post('/api/work/v3/api/ApplyAfterSales', data) //申请售后
  let afterSaleRefundMoney = (params) => vm.$u.get('/api/orders/v3/afterSales/getOrderRefundMoney', params) //售后可退金额

  // 售后详情
  let afterSaleDetail = (params) => vm.$u.get('/api/work/v3/api/getAfterSales', params) //售后详情信息（仅退款、退货退款，换货）
  let returnAndexchangeFillInfo = (data) => vm.$u.post('/api/work/v3/api/enterReturnInfo', data) //退换货填写退货信息
  const getPickUpDateList = (params) => vm.$u.get('/api/work/v3/api/getPickUpDateRange', params)

  // oss上传板块
  let ossUpload = (params) => vm.$u.get('/api/oss/v3/sign', params) //oss上传

  // 支付抽奖
  let payLuckyDrawList = (params) => vm.$u.get('/api/marketing-conf/v3/game/getRotaryDraw', params) //支付抽奖列表
  let payLuckyDraw = (params) => vm.$u.get('/api/marketing-conf/v3/game/luckdraw', params) //支付抽奖大转盘
  let payLuckyDrawCount = (params) => vm.$u.get('/api/marketing-conf/v3/game/getUserOrderCount', params) //支付抽奖大转盘数量

  // 认证板块
  let certificationInfo = (params) => vm.$u.get('/api/user/v3/authentication/info', params) //认证信息
  let certificationList = (params) => vm.$u.get('/api/user/v3/authentication/levelList', params) //认证列表
  let submitCertification = (data) => vm.$u.post('/api/user/v3/authentication/submit', data) //提交认证申请

  // 兔头商城板块
  let rabbitCombine = (params) => vm.$u.get('/api/openapi/v3/rabbit/home', params) //兔头商城首页聚合接口
  let rabbitCouponDetail = (params) => vm.$u.get('/api/commodities/v3/rabbitCoupon/detail', params) //兔头优惠券详情
  let rabbitExchangeRecord = (params) => vm.$u.get('/api/user/v3/profile/RabbitRecord', params) //兔头兑换记录
  let rabbitExchangeCoupon = (data) => vm.$u.post('/api/commodities/v3/rabbitCoupon/exchange', data) //兔头兑换优惠券
  let rabbitGoodsDetail = (params) => vm.$u.get('/api/commodities/v3/rabbit/detail', params) //兔头商品详情
  let rabbitCreateOrder = (data) => vm.$u.post('/api/orders/v3/rabbit/create', data) //兔头商品创建订单

  // 大转盘
  let largeTurntableLuckyDrawList = (params) => vm.$u.get('/api/marketing-conf/v3/game/getRabbitRotaryDraw', params) //大转盘查询抽奖列表
  let largeTurntableInfo = (params) => vm.$u.get('/api/marketing-conf/v3/game/getUserLotteryInfo', params) //大转盘查询抽奖信息
  let largeTurntableLuckyDraw = (params) => vm.$u.get('/api/marketing-conf/v3/game/rabbitluckdraw', params) //大转盘抽奖
  let largeTurntablePriceRecord = (params) => vm.$u.get('/api/marketing-conf/v3/game/rabbitShareDrawList', params) //大转盘抽奖记录
  let newRabbitLuckDraw = (params) => vm.$u.get('/api/marketing-conf/v3/game/newRabbitLuckDraw', params) //新人大转盘抽奖
  let getNewRabbitRotaryDraw = (params) => vm.$u.get('/api/marketing-conf/v3/game/getNewRabbitRotaryDraw', params) //新人抽奖配置列表
  let newRabbitShareDrawList = (params) => vm.$u.get('/api/marketing-conf/v3/game/newRabbitShareDrawList', params) //新人兔头幸运转盘抽奖记录
  let getNewcomerLotteryInfo = (params) => vm.$u.get('/api/marketing-conf/v3/game/getNewcomerLotteryInfo', params) //新人兔头幸运转盘查询用户抽奖信息
  let newcomerShare = (data) => vm.$u.post('/api/marketing-conf/v3/game/newcomerShare', data) //新人分享增加抽奖次数

  // 等级
  let userLevelInfo = (params) => vm.$u.get('/api/user/v3/user/getUserLevelInfo', params) //用户等级信息

  // 更多酒款列表（卡片点击更多跳转的列表）
  let moreThanWineList = (params) => vm.$u.get('/api/marketing-conf/v3/cardgoodslive/clientgoodslist', params) //更多酒款列表

  // 门店
  let storeAddRegister = (data) => vm.$u.post('/api/osms/v3/user/addregister', data) //门店注册用户
  let storeList = (params) => vm.$u.get('/api/newosms/v3/store/list', params) //门店列表
  let storeInfo = (params) => vm.$u.get('/api/osms/v3/goods/getstore', params) //门店信息
  let storeFilterList = (params) => vm.$u.get('/api/osms/v3/attribute/getattribute', params) //门店筛选列表
  let storeGoodsList = (data) => vm.$u.post('/api/osms/v3/goods/goodslist', data) //门店商品列表
  let storeGoodsDetail = (params) => vm.$u.get('/api/osms/v3/goods/getgoods', params) //门店商品详情
  let storeGoodsAddShoppingCart = (data) => vm.$u.post('/api/osms/v3/shopping/add', data) //门店商品加入购物车
  let storeShoppingCartList = (params) => vm.$u.get('/api/osms/v3/shopping/list', params) //门店购物车列表
  let storeShoppingCartChangeNum = (data) => vm.$u.post('/api/osms/v3/shopping/numsdeal', data) //门店购物车数量加减
  let storeShoppingCartDel = (data) => vm.$u.post('/api/osms/v3/shopping/del', data) //门店购物车删除
  let storeOrderPrice = (data) => vm.$u.post('/api/osms/v3/order/getfee', data) //门店订单金额
  let storeCreateOrder = (data) => vm.$u.post('/api/osms/v3/order/add', data) //门店创建订单
  let storePay = (data) => vm.$u.post('/api/osms/v3/pay/request', data) //门店支付
  let storeOrderList = (params) => vm.$u.get('/api/osms/v3/order/list', params) //门店订单列表
  let storeCanInvoiceList = (params) => vm.$u.get('/api/osms/v3/order/screenStore', params) //支持可开票且用户消费过的门店列表
  let storeOrderInvoiceList = (data) => vm.$u.post('/api/osms/v3/receipt/invoiceList', data) //门店订单待开票列表
  let storeOrderOpenInvoice = (data) => vm.$u.post('/api/osms/v3/receipt/invoice', data) //门店订单开票接口
  let storeOrderRefund = (data) => vm.$u.post('/api/osms/v3/order/applyaftersale', data) //门店订单申请退款
  let storeOrderConfirmReceipt = (data) => vm.$u.post('/api/osms/v3/order/confirmreceipt', data) //门店订单确认收货
  let experienceCouponList = (params) => vm.$u.get('/api/osms/v3/coupon/couponlist', params) //体验券列表
  let experienceCouponInfo = (params) => vm.$u.get('/api/osms/v3/coupon/couponget', params) //体验券信息
  let experienceCouponTake = (params) => vm.$u.get('/api/osms/v3/coupon/coupontake', params) //体验券领取
  const storeDeliveryRange = (params) => vm.$u.get('/api/newosms/v3/store/deliveryRangeList', params) //是否在门店配送范围内

  // 商家板块
  let businessShipmentAddressList = (params) => vm.$u.get('/api/vmall/v3/common/businesshours', params) //商家范围内发货地址列表
  let businessOrderGoodsMatchInfo = (data) => vm.$u.post('/api/vmall/v3/common/shipmentypefreight', data) //商家获取运费、3小时达数据（订单商品列表做匹配使用）
  let businessGetGoodsListIdentification = (url, data) => vm.$u.post(url, data) //根据定位获取商家商品距离（商品列表获取标签时使用）
  let businessCityList = (params) => vm.$u.get('/api/vmall/v3/invoice/city/default', params) //商家城市接口
  let businessShipTips = (params) => vm.$u.get('/api/vmall/v3/common/deliverytime', params) //商家发货时间提示语

  // 酒评板块
  let wineParams = (params) => vm.$u.get('/api/wiki/v3/wineparam', params) //酒款参数（香味、配餐、品味...）
  const getWineCommentList = (params) => vm.$u.get('/api/community/v3/wineEvaluation/orderWineEvaluationList', params)
  const createWineComment = (data) => vm.$u.post('/api/community/v3/wineEvaluation/create', data)
  const getWineCommentDetail = (params) => vm.$u.get('/api/community/v3/wineEvaluation/detail', params)
  const getCommunityTopicList = (params) => vm.$u.get('/api/community/v3/topic/index', params)
  const getCommunityTopicDetail = (params) => vm.$u.get('/api/community/v3/topic/detail', params)

  // 工具类接口
  let writeOffCode = (params) => vm.$u.get('/api/wineparty/v3/wineparty/getCancelCode', params) //核销码
  let regionId = (params) => vm.$u.get('/api/user/v3/regional/getAreaNumber', params) //地区id
  let channelKeyword = (params) => vm.$u.get('/api/marketing-conf/v3/defaultkeyword/getKeyword', params) //频道关键字
  let newPeopleMarketingAd = (params) => vm.$u.get('/api/marketing-conf/v3/ad/prescription', params) //新人营销广告
  const registerUidByTencentMoments = (data) => vm.$u.post('/api/maidian/v3/auction/registerUidByTencentMoments', data) //将用户设置为腾讯朋友圈广告过来的标识
  let newPeopleCouponPackage = (data) => vm.$u.post('/api/coupon/v3/couponpackage/grantpackage', data) //优惠券包发放

  // 酒会活动接口
  const winepartyActivityDetail = (params) => vm.$u.get('/api/invite/v3/winepartyActivity/detail', params)
  const winepartyActivityGoodsList = (params) => vm.$u.get('/api/invite/v3/winepartyActivityGoods/list', params)

  // 拉新活动接口
  const inviteActivityDetail = (params) => vm.$u.get('/api/invite/v3/inviteActivity/detail', params)
  const inviteActivityGoodsList = (params) => vm.$u.get('/api/invite/v3/inviteActivityGoods/list', params)
  const getInviteActivityTicket = (params) => vm.$u.post('/api/invite/v3/inviteActivity/send', params)

  const getCouponDetail = (params) => vm.$u.get('/api/coupon/v3/couponpackage/details', params)
  const getNewcomerGoodsList = (params = { onsale_status: 2 }) =>
    vm.$u.get('/api/commodities/v3/marketing/getNewcomerList', params)

  const reportBuryDot = (params) => vm.$u.post('/api/maidian/v3/report/report', params)

  // 拍卖板块
  const getPersonAuctionOrderInfo = (params) => vm.$u.get('/api/auction-goods/v3/goods/earnestOrder', params)
  const searchAuctionMyBuyingList = (params) => vm.$u.get('/api/auction-order/v3/order/myPurchasedList', params)
  const findProductListByPeriod = (params) => vm.$u.get('/api/auction-goods/v3/goods/find', params)
  const getPersonAuctionConfig = () => vm.$u.get('/api/auction-goods/v3/goods/personageOptions')
  const createPersonAuctionGoods = (params) => vm.$u.post('/api/auction-goods/v3/goods/add', params)
  const getPersonAuctionGoods = (params) => vm.$u.get('/api/auction-goods/v3/goods/otherParameterDetail', params)
  const getPersonAuctionGoodsRelease = (params) => vm.$u.get('/api/auction-goods/v3/goods/userReleaseDetail', params)
  const resubmitPersonAuctionGoods = (params) => vm.$u.post('/api/auction-goods/v3/goods/resend', params)
  const preResubmitPersonAuctionGoods = (params) => vm.$u.post('/api/auction-goods/v3/goods/perResend', params)
  const getShipAddressRegionList = () => vm.$u.get('/api/auction-goods/v3/freightEstimate/options')
  const getFreightEstimate = (params) => vm.$u.get('/api/auction-goods/v3/freightEstimate/listByToAddr', params)
  const searchAuctionMyCreateList = (params) => vm.$u.get('/api/auction-goods/v3/goods/userRelease', params)
  const getPendDeliverCount = (params) => vm.$u.get('/api/auction-goods/v3/goods/awaitDeliver', params)
  const abandonPersonAuction = (params) => vm.$u.post('/api/auction-goods/v3/goods/abandon', params)
  const getAuctionPickUpList = (params) => vm.$u.post('/api/auction-goods/v3/goods/standardCalendar', params)
  const deliverPersonGoods = (params) => vm.$u.post('/api/auction-goods/v3/goods/receivePickUpOrder', params)
  const getAuctionAdDetail = (params) => vm.$u.get('/api/auction-goods/v3/bannerActivity/detail', params)
  const getAuctionIndexBannerList = () => vm.$u.get('/api/auction-goods/v3/banner/homePageList')
  const getAuctionIndexAreaList = () => vm.$u.get('/api/auction-goods/v3/category/areaList')
  const searchAuctionIndexGoodsList = (params) => vm.$u.get('/api/auction-goods/v3/goods/indexGoods', params)
  const auctionIndexGoodsListFirstBanner = (params) => vm.$u.get('/api/auction-goods/v3/goods/getIndexBanner ', params)
  const searchAuctionActivityGoodsList = (params) => vm.$u.get('/api/auction-goods/v3/activityGoods/goodsList', params)
  const getAuctionGoodsDetail = (params) => vm.$u.get('/api/auction-goods/v3/goods/getGoodsDetail', params)
  const getAuctionRecommendGoodsList = () => vm.$u.get('/api/auction-goods/v3/goods/recommendGoods')
  const getAuctionHotRecommendGoodsList = () => vm.$u.get('/api/auction-goods/v3/goods/getHotRecommend')
  const createAuctionGoods = (params) => vm.$u.post('/api/auction-goods/v3/goods/createGoods', params)
  const searchAuctionMyGoodsList = (params) => vm.$u.get('/api/auction-goods/v3/goods/getGoodsList', params)
  const delAuctionGoods = (params) => vm.$u.post('/api/auction-goods/v3/goods/delGoods', params)
  const searchAuctionCertificateList = (params) => vm.$u.get('/api/auction-goods/v3/goods/getCertificate', params)
  const updateAuctionCertificateShareStatus = (params) =>
    vm.$u.post('/api/auction-goods/v3/goods/updateCertificate', params)
  const addEnjoyAuctionGoods = (params) => vm.$u.post('/api/auction-goods/v3/goodsLikes/like', params)
  const cancelEnjoyAuctionGoods = (params) => vm.$u.post('/api/auction-goods/v3/goodsLikes/dislike', params)
  const batchCancelEnjoyAuctionGoods = (params) => vm.$u.post('/api/auction-goods/v3/goodsLikes/batchDislike', params)
  const searchEnjoyAuctionGoodsList = (params) => vm.$u.get('/api/auction-goods/v3/goodsLikes/goodsListFormat', params)
  const searchRemindAuctionGoodsList = (params) => vm.$u.get('/api/auction-goods/v3/goods/getUserFollow', params)
  const editRemindAuctionGoods = (params) => vm.$u.post('/api/auction-goods/v3/goods/UserFollow', params)
  const getAuctionBidRecords = (params) => vm.$u.get('/api/auction/v3/history', params)
  const auctionBid = (params) => vm.$u.get('/api/auction/v3/bid', params)
  const auctionBuyerOrderList = (params) => vm.$u.get('/api/auction-order/v3/order/buyerOrderList', params) //我拍到的订单列表
  const auctionBuyerAfterSaleList = (params) =>
    vm.$u.get('/api/auction-order/v3/afterSales/buyerAfterSalesList', params) //买家售后列表
  const auctionSellerOrderList = (params) => vm.$u.get('/api/auction-order/v3/order/sellerOrderList', params) //我卖出的订单列表
  const auctionSellerAfterSaleList = (params) =>
    vm.$u.get('/api/auction-order/v3/afterSales/sellerAfterSalesList', params) //我卖出的订单列表
  const auctionRemindNotice = (data) => vm.$u.post('/api/auction-order/v3/order/remindNotice', data) //拍卖提醒通知
  const auctionConfirmReceipt = (data) => vm.$u.post('/api/auction-order/v3/order/confirmReceipt', data) //拍卖确认收货
  const auctionOrderDetail = (params) => vm.$u.get('/api/auction-order/v3/order/orderDetail', params) //买家/卖家订单详情
  const auctionOrderEvaluate = (data) => vm.$u.post('/api/auction-goods/v3/evaluate/userAdd', data) //拍卖订单评价
  const auctionOrderEvaluateList = (params) => vm.$u.get('/api/auction-goods/v3/evaluate/listByUser', params) //评价列表
  const auctionOrderEvaluateListLike = (params) => vm.$u.post('/api/auction-goods/v3/evaluateLikes/like', params) //评价列表点赞
  const auctionOrderEvaluateListDislike = (params) => vm.$u.post('/api/auction-goods/v3/evaluateLikes/dislike', params) //评价列表取消点赞
  const auctionParticipateList = (params) => vm.$u.get('/api/auction-goods/v3/goods/getUserParticipate', params) //我的参拍列表
  const auctionParticipateListDel = (data) => vm.$u.post('/api/auction-goods/v3/goods/delUserParticipate', data) //删除我的参拍

  const auctionAfterSaleApply = (data) => vm.$u.post('/api/auction-order/v3/afterSales/apply', data) //拍卖申请售后
  const auctionAfterSaleDetail = (params) => vm.$u.get('/api/auction-order/v3/afterSales/afterSalesDetail', params) //拍卖售后详情
  const auctionAfterSaleDetailAudit = (data) => vm.$u.post('/api/auction-order/v3/afterSales/audit', data) //同意/拒绝售后申请（待处理）
  const auctionAfterSaleDetailSellerReceiptDeal = (data) =>
    vm.$u.post('/api/auction-order/v3/afterSales/sellerReceiptDeal', data) //卖家同意/拒绝收货（待收货）
  const auctionPickupPeriod = (data) => vm.$u.post('/api/auction-order/v3/afterSales/getStandardCalendar', data) //获取取件时段
  const auctionSubmitPickupInfo = (data) => vm.$u.post('/api/auction-order/v3/afterSales/submitStandardCalendar', data) //提交上门取件信息
  const auctionAfterSalesSpeed = (params) => vm.$u.get('/api/auction-order/v3/afterSales/afterSalesSpeed', params) //售后进度
  const getAuctionGoodsComments = (params) => vm.$u.get('/api/auction-goods/v3/comment/listByGoods', params)
  const addAuctionGoodsComment = (params) => vm.$u.post('/api/auction-goods/v3/comment/userAdd', params)
  const addEnjoyAuctionGoodsComment = (params) => vm.$u.post('/api/auction-goods/v3/CommentLikes/like', params)
  const cancelEnjoyAuctionGoodsComment = (params) => vm.$u.post('/api/auction-goods/v3/CommentLikes/dislike', params)
  const createAuctionEarnestOrder = (params) => vm.$u.post('/api/auction-order/v3/earnest/create', params)
  const getAuctionEarnestOrderDetail = (params) => vm.$u.get('/api/auction-order/v3/earnest/earnestInfo', params)
  const getAuctionEarnestSimpleOrderDetail = (params) => vm.$u.get('/api/auction-order/v3/earnest/getOne', params)
  const getAuctionEarnestStatus = (params) => vm.$u.post('/api/auction-order/v3/earnest/isPayment', params)
  const searchAuctionFundsList = (params) => vm.$u.get('/api/auction-order/v3/earnest/fundDetails', params)
  const searchAuctionFundsListNew = (params) => vm.$u.get('/api/auction-order/v3/earnest/fundDetailsList', params)
  const getAuctionFundsDetail = (params) => vm.$u.post('/api/auction-order/v3/earnest/earnestDetail', params)
  const getAuctionFundsStats = () => vm.$u.get('/api/auction-order/v3/earnest/getCostStatistics')
  const searchAuctionEarnestList = (params) => vm.$u.get('/api/auction-order/v3/earnest/myEarnestList', params)
  const addAuctionRNInfo = (params) => vm.$u.post('/api/auction-goods/v3/ac/authAdd', params)
  const editAuctionRNInfo = (params) => vm.$u.post('/api/auction-goods/v3/ac/authEdit', params)
  const getAuctionRNInfo = (params) => vm.$u.get('/api/auction-goods/v3/ac/detailByType', params)
  const getAuctionUserInfo = () => vm.$u.get('/api/auction-goods/v3/user/info')
  const getAuctionMineStats = () => vm.$u.get('/api/auction-goods/v3/user/quantityStatistics')
  const getAuctionMsg = () => vm.$u.get('/api/message/v3/notice/noticeTypeAll')
  const getAuctionMsgDetail = (params) => vm.$u.get('/api/message/v3/notice/noticeList', params)
  const searchAuctionCreditValuesList = (params) => vm.$u.get('/api/user/v3/auction/CreditScoreRecordsList', params)
  const addAuctionBank = (params) => vm.$u.post('/api/auction-order/v3/bank/create', params)
  const removeAuctionBank = (params) => vm.$u.post('/api/auction-order/v3/bank/relieve', params)
  const searchAuctionBankList = (params) => vm.$u.get('/api/auction-order/v3/bank/lists', params)
  const createAuctionPayeeAccount = (params) => vm.$u.post('/api/auction-order/v3/account/create', params)
  const updateAuctionPayeeAccount = (params) => vm.$u.post('/api/auction-order/v3/account/update', params)
  const searchAuctionPayeeAccounts = (params = {}) => vm.$u.get('/api/auction-order/v3/account/lists', params)
  const searchGrapeVarietyList = (params = {}) => vm.$u.get('/api/wiki/v3/grape/brief', params)
  const searchWineGradeOrgList = (params = {}) => vm.$u.get('/api/wiki/v3/winecexpert', params)
  const getJsapiSign = (params) => vm.$u.post('/api/wechat/v3/jsapi/sign', params)
  const appPayment = (params) => vm.$u.post('/api/payment/v3/weAndAli/pay', params)
  const getWxPayOpenIdByCode = (params) => vm.$u.get('/api/payment/v3/we/openid', params)
  const getWxOABindStatus = () => vm.$u.get('/api/user/v3/wechat/IsFollowOfficialAccount')
  const bindWxOA = (params) => vm.$u.post('/api/user/v3/wechat/BindingOfficiAlaccountOpenid', params)
  const getPayToPublicKefu = (params) => vm.$u.get('/api/user/v3/user/PaymentQrCode', params)

  // 再来一单板块
  const oneMoreOrder = (data) => vm.$u.post('/api/orders/v3/repurchasestatistics/create', data) //再来一单
  const iWant = (data) => vm.$u.post('/api/orders/v3/repurchasestatistics/want/create', data) //我想要
  const addWishList = (data) => vm.$u.post('/api/orders/v3/repurchasestatistics/saveperiodrepurchmsg', data) //加入心愿清单
  const wishList = (params) => vm.$u.get('/api/orders/v3/repurchasestatistics/wishlist', params) //心愿清单列表
  const wishListRemove = (data) => vm.$u.post('/api/orders/v3/repurchasestatistics/remove', data) //心愿清单列表移除

  const queryActivityGoodsList = (params) => vm.$u.get('/api/activity/v3/goods/actLabelGoods', params)
  const getActivityByPeriods = (params) => vm.$u.get('/api/activity/v3/goods/getActivityByPeriods', params)
  const getWineCommentRHCount = () => vm.$u.get('/api/community/v3/wineEvaluation/wineRabbit')

  const getSecondCombine = (params) => vm.$u.get('/api/commodities_server/v3/second_home/aggregation', params)
  const getRandomProductCategory = (params) => vm.$u.get('/api/wiki/v3/productcategory/typerandom', params)
  const getSecondRecommendList = (params) => vm.$u.post('/api/go-recommend/v3/second/secondHome', params)
  const filterSecondDataList = (params) => vm.$u.get('/api/vmall/v3/goods/goodsListByLabel', params)
  const getSecondGoodsRecommendList = (params) => vm.$u.get('/api/go-recommend/v3/second/secondGoodsRecommend', params)
  const reportUserPortrait = (params) => vm.$u.post('/api/commodities/v3/userPortrait/feedback', params)
  const reportUserPortraitLabel = (params) => vm.$u.post('/api/commodities/v3/userPortrait/postUserPortrait', params)

  const secondSearch = (params) => vm.$u.get('/api/commodities/v3/second/search', params)
  const getSecondRecommendIncrement = (params) =>
    vm.$u.post('/api/go-distribution/v3/secondaggregate/clientlabel', params)
  const getSearchFeedbackCount = (params) => vm.$u.get('/api/commodities/v3/userQueryLog/userList', params)
  const addSeachFeedback = (params) => vm.$u.post('/api/commodities/v3/userQueryLog/add', params)
  // 新人板块
  const newPeopleCouponActivityInfo = (params) => vm.$u.get('/api/user/v3/newusers/activity', params) //领券信息+新人列表
  const newPeopleReceiveBenefits = (data) => vm.$u.post('/api/user/v3/newusers/receive_benefits', data) //领券

  // 订金板块
  const depositOrderList = (params) => vm.$u.get('/api/orders/v3/order/depositList', params) //订金订单列表
  const depositOrderDetail = (params) => vm.$u.get('/api/orders/v3/order/depositDetail', params) //订金订单详情

  // 首三单板块
  const topThreeOrderNums = (params) => vm.$u.get('/api/orders/v3/order/getUserTopThreeOrderNums', params) //获取用户首三单订单个数

  // 启动页切换板块
  let userChangePageConfig = (data) => vm.$u.post('/api/user/v3/profile/setSystemConfig', data) //切换用户页面配置
  const addInvestigatesFeedback = (params) =>
    vm.$u.post('/api/invite/v3/satisfactionSurvey/submitQuestionnaire', params)
  const getInventoryRemainInfo = (params) =>
    vm.$u.get('/api/commodities/v3/package/getPeriodsRemainingOrderQuantity', params)
  const updateOrderAddressValidation = (params) => vm.$u.get('/api/work/v3/api/UpdateAddressPreValidation', params)
  const updateOrderAddress = (params) => vm.$u.post('/api/work/v3/api/ApplyAfterSales', params)
  const getAuctionEarnestCouponList = () => vm.$u.get('/api/coupon/v3/coupon/getAuctionCouponInfo')
  const recordPreSalesSource = (params) => vm.$u.post('/api/orders/v3/preSales/recordPreSalesShare', params)
  const updateOrderTsStatus = (params) => vm.$u.post('/api/orders/v3/order/stagingOrder', params)
  const upgradeColdChain = (params) => vm.$u.post('/api/orders/v3/order/upgradeColdChain', params)
  const cancelAfterSale = (params) => vm.$u.post('/api/work/v3/api/CancelAfterSales', params)
  const updateOrderInvoiceInfo = (params) => vm.$u.post('/api/orders/v3/order/applyBilling', params)
  const getUserReturningInfo = () => vm.$u.get('/api/user/v3/activity/getRegressionInfo')
  const getGoodsFullReductionLabel = (params) =>
    vm.$u.post('/api/commodities_server/v3/marketing/GetFullReductionActivityLabel', params)
  const reportWasmSupport = () => vm.$u.post('/api/maidian/v3/report/wasmReport')
  // 一些公共接口
  // ...
  const storeApplyInvoice = (params) => vm.$u.post('/api/newosms/v3/invoice/SubmitOrderInvoicing', params)
  const getStoreApplyInvoiceStatus = (params) => vm.$u.get('/api/newosms/v3/invoice/getOrderIsInvoiced', params)
  const getChapterList = (params) => vm.$u.get('/api/academy/v3/academy/getChapterList', params)
  const getChapterDetail = (params) => vm.$u.get('/api/academy/v3/academy/getChapterDetail', params)
  const addCollection = (params) => vm.$u.post('/api/academy/v3/academy/addCollection', params)
  const getExamList = (params) => vm.$u.get('/api/academy/v3/academy/getExamList', params)
  const getExamQuestion = (params) => vm.$u.get('/api/academy/v3/academy/getExamQuestion', params)
  const submitAnswer = (params) => vm.$u.post('/api/academy/v3/academy/submitAnswer', params)
  const resetExam = (params) => vm.$u.post('/api/academy/v3/academy/resetExam', params)
  const cancelExam = (params) => vm.$u.post('/api/academy/v3/academy/cancelExam', params)
  const submitExam = (params) => vm.$u.post('/api/academy/v3/academy/submitExam', params)
  const getExamQuestionList = (params) => vm.$u.get('/api/academy/v3/academy/getExamQuestionList', params)

  //学堂
  const studyTopicList = (params) => vm.$u.get('/api/academy/v3/academy/getTopicList', params) //课题列表
  const studyBannerList = (params) => vm.$u.get('/api/academy/v3/academy/banner', params) //Banner列表
  const studyRankingList = (params) => vm.$u.get('/api/academy/v3/academy/getScoreRank', params) //排行榜列表
  const getQuestionCollection = (params) => vm.$u.get('/api/academy/v3/academy/getQuestionCollection', params) //试题收藏列表
  const getChapterCollection = (params) => vm.$u.get('/api/academy/v3/academy/getChapterCollection', params) //章节收藏列表
  const getChapterScanRecord = (params) => vm.$u.get('/api/academy/v3/academy/getChapterScanRecord', params) //章节学习记录
  const getExamScanRecord = (params) => vm.$u.get('/api/academy/v3/academy/getExamScanRecord', params) //测试学习记录
  const getBadgeList = (params) => vm.$u.get('/api/academy/v3/academy/badgeList', params) //我的证书列表
  const getBadgeDetails = (params) => vm.$u.get('/api/academy/v3/academy/getBadgeInfo', params) //我的证书详情
  const PostUserAuth = (params) => vm.$u.post('/api/academy/v3/academy/userAuth', params) //提交真实名称
  const IndexClientlist = (params) => vm.$u.get('/api/marketing-conf/v3/column/clientlist', params) // 首页栏目
  const secondHomeCard = (params) => vm.$u.get('/api/commodities_server/v3/second_home/aggregation_v2', params) // 秒发首页
  const filtergoodslist = (params) => vm.$u.get('/api/marketing-conf/v3/cardgoodslive/filtergoodslist', params)
  const reservationGoods = (params) => vm.$u.post('/api/commodities/v3/periods/reservation', params)
  const filtergoodslistColum = (params) => vm.$u.get('/api/marketing-conf/v3/column/filtergoodslist', params)
  const isReservation = (params) => vm.$u.post('/api/commodities/v3/periods/isReservation', params)
  const lastOrderDetail = (params) => vm.$u.get('/api/orders/v3/order/lastOrderDetail', params)
  // 将各个定义的接口名称，统一放进对象挂载到vm.$u.api(因为vm就是this，也即this.$u.api)下
  const getShareInfo = (params) => vm.$u.get('/api/commodities/v3/marketing/getShareInfo', params)
  const myReservation = (params) => vm.$u.get('/api/commodities/v3/periods/myReservation', params) // 预约列表
  const cancelReservation = (params) => vm.$u.post('/api/commodities/v3/periods/cancelReservation', params) // 取消预约
  const upEva = (params) => vm.$u.post('/api/community/v3/wineEvaluation/upEva', params) // 编辑酒评
  const updateOrderTsTime = (params) => vm.$u.post('/api/orders/v3/order/upTsTime', params) // 修改暂存时间
  const secondConfig = (params) => vm.$u.get('/api/marketing-conf/v3/common/secondConfig', params) // 主题
  const cardGetfilter = (params) => vm.$u.get('/api/marketing-conf/v3/columngoods/getfilter', params) // 主题
  const getfiltergoodslist = (params) => vm.$u.get('/api/marketing-conf/v3/columngoods/getfiltergoodslist', params) // 主题

  // 社区
  const getCommunityList = (params) => vm.$u.get('/api/contentaudit/v3/comment/appRecommendList', params) //社区列表
  const getTopicList = (params) => vm.$u.get('/api/community/v3/topic/index', params)
  const getTopicDetail = (params) => vm.$u.get('/api/community/v3/topic/detail', params) // 新增话题详情接口
  const postsLike = (params) => vm.$u.post('/api/community/v3/posts/postsLike', params) // 点赞
  const focusUser = (params) => vm.$u.post('/api/user/v3/attention/operate', params) // 关注
  const topicForList = (params) => vm.$u.get('/api/go-community/v3/topic/list', params) // 话题下的帖子
  const wineEvaluationList = (params) => vm.$u.get('/api/community/v3/wineEvaluation/wineEvaluationList', params) // 话题下的帖子
  const wineEvaluationLike = (params) => vm.$u.post('/api/community/v3/wineEvaluation/like', params) // 酒评点赞
  const createPost = (params) => vm.$u.post('/api/community/v3/posts/create', params) // 发布帖子
  const postsDetail = (params) => vm.$u.get('/api/community/v3/posts/postsDetail', params) // 帖子详情
  const postsCommentList = (params) => vm.$u.get('/api/community/v3/PostsComment/postsCommentList', params) // 帖子评论列表
  const commentList = (params) => vm.$u.get('/api/community/v3/wineComment/commentList', params) // 酒评评论列表
  const postMakeComment = (params) => vm.$u.post('/api/community/v3/PostsComment/makeComment', params) // 帖子回复
  const wineMakeComment = (params) => vm.$u.post('/api/community/v3/wineComment/create', params) // 酒评回复
  const getOtherData = (params) => vm.$u.post('/api/contentaudit/v3/comment/getOtherData', params) // 列表是否点赞和评论数量
  const collection = (params) => vm.$u.post('/api/community/v3/wineEvaluation/collection', params) // 收藏酒评
  const getUserInfo = (params) => vm.$u.get('/api/user/v3/community/getUserInfo', params) // 个人信息
  const postsList = (params) => vm.$u.get('/api/community/v3/posts/postsList', params) // 他的帖子列表
  const selfPostsList = (params) => vm.$u.get('/api/community/v3/posts/selfPostsList', params) // 我的帖子列表
  const myWineEvaluationList = (params) => vm.$u.get('/api/community/v3/wineEvaluation/myWineEvaluationList', params) // 我的酒评
  const updatePost = (params) => vm.$u.post('/api/community/v3/posts/update', params) // 编辑帖子
  const getVOSUid = (params) => vm.$u.get('/api/authority/v3/admin/conditionQueryList', params) // 获取中台id
  const deletePost = (params) => vm.$u.post('/api/community/v3/posts/status', params) // 删除帖子
  const vmProductReport = (params) => vm.$u.post('/api/maidian/v3/report/vmProductReport', params) // 浏览上报

  const myBalanceHistory = (params) => vm.$u.get('/api/user/v3/balance/myBalanceHistory', params) // 我的余额记录
  const giftCardList = (params) => vm.$u.get('/api/user/v3/giftCards/giftCard', params) // 购买礼品卡列表
  const myGiftCardsList = (params) => vm.$u.get('/api/user/v3/giftCards/myCards', params) // 我的礼品卡列表
  const myCurrentBalance = (params) => vm.$u.get('/api/user/v3/balance/current_balance', params) // 我的余额
  const giftCardOrderCreate = (params) => vm.$u.post('/api/user/v3/giftCards/orderCreate', params) // 充值卡创建订单
  const giftCarduseVirtual = (params) => vm.$u.post('/api/user/v3/giftCards/useVirtual', params) // 使用礼品卡
  const giftCardSend = (params) => vm.$u.post('/api/user/v3/giftCards/giftTransfer', params) // 赠送礼品卡
  const balancePay = (params) => vm.$u.post('/api/orders/v3/order/balancePay', params) // 余额支付
  const giftCardsDetail = (params) => vm.$u.get('/api/user/v3/giftCards/giftInfo', params) // 礼品卡详情
  const giftCardsInfo = (params) => vm.$u.get('/api/user/v3/giftCards/giftCardGoodsInfo', params) // 礼品卡详情
  vm.$u.api = {
    vmProductReport,
    getfiltergoodslist,
    cardGetfilter,
    //注册
    deletePost,
    updatePost,
    secondConfig,
    getCommunityList,
    updateOrderTsTime,
    upEva,
    cancelReservation,
    getShareInfo,
    myReservation,
    lastOrderDetail,
    getBadgeDetails,
    filtergoodslistColum,
    isReservation,
    filtergoodslist,
    reservationGoods,
    secondHomeCard,
    PostUserAuth,
    IndexClientlist,
    getBadgeList,
    getExamScanRecord,
    getExamQuestionList,
    getChapterCollection,
    getChapterScanRecord,
    getQuestionCollection,
    getExamList,
    submitExam,
    cancelExam,
    resetExam,
    submitAnswer,
    getExamQuestion,
    getChapterList,
    getChapterDetail,
    getVersionInfo,
    addCollection,
    // 登录板块
    quickLogin, //微信快捷登录
    sendSmsCode, //发送验证码
    loginByCode, //登录（验证码方式）

    // 首页板块
    indexCombine, //首页聚合接口
    newsDetail, //首页快报详情
    liveBroadcastReservation, //直播预约
    hoverButtonInfo, //悬浮按钮

    // 我的板块
    userData, //获取用户数据
    signIn, //用户签到
    mineFuncList, //个人中心功能列表
    userSpecifiedData, //用户指定数据
    setNewsPush, //设置消息推送
    agreeProtocol, //同意用户协议
    getFootprintList, //我的足迹列表
    delFootprint, //删除足迹

    // 优惠券板块
    couponList, //优惠券板块
    getCouponList,
    getCouponListByOrder,
    orderCoupon, //获取订单可用优惠劵
    couponDetail, //获取优惠券详情

    // 个人信息板块
    updateUserInfo, //修改用户信息

    // 日常任务板块
    dailyTasksList, //日常任务列表

    // 消息中心
    messageUnreadNum, //未读消息数量
    messagePushList, //消息推送列表
    messageClean, //清空消息

    // 收货地址管理板块
    addressList, //收货地址列表
    regionList, //地区树形列表（JSON文件）
    addAddress, //添加收货地址
    deleteAddress, //删除收货地址
    updateAddress, //修改收货地址
    setDefaultAddress, //设置默认地址
    addressAiMatch, //收货地址智能匹配接口

    // 发票抬头板块
    invoiceList, //发票列表
    addInvoice, //添加发票信息
    deleteInvoice, //删除发票信息
    updateInvoice, //修改发票信息
    openInvoice, //开发票

    // 历史发票
    invoiceHistoryList, //历史发票列表
    invoiceHistoryDetail, //历史发票详情
    invoiceImage, //历史发票图片

    // 闪购板块
    flashIndexGoodsList, //闪购首页商品列表（默认进入页面时请求）
    flashGoodsList, //闪购筛选商品列表（筛选时调取 闪购：0、秒发：1、跨境：2、尾货：3 ）
    flashChannelList, //闪购首页频道列表
    flashFilterList, //闪购过滤列表（类型、国家、关键字）
    getFlashLiquorFilterList,
    getFlashKjFilterList,
    getFlashAggregateData,

    // 秒发板块
    secondCombine, //秒发聚合接口
    secondHairFilterList, //秒发过滤列表（类型、国家、关键字）
    secondGoldAreaList, //秒发金刚区列表
    secondHairSonPageSecondClassifyList, //秒发二级页面二级筛选列表
    secondHairSonPageList, //秒发二级页面列表

    // 社区板块
    fansAndAttentionList, //用户关注、粉丝列表
    userAttenAndCncelAtten, //用户关注、取消关注

    // 商品详情板块
    shoppingCartNum, //购物车数量
    goodsDetailJson, //商品详情json文件
    goodsDetail, //商品详情
    groupInfo, //拼团信息
    packageDetail, //查询套餐信息
    vmallPackageDetail, //查询商家套餐详情（商品）
    secondPackageDetail, //查询秒发套餐详情（商品）
    goodsCollectionStatus, //商品收藏状态
    goodsCollection, //商品收藏
    goodsDiscountArea, //商品详情优惠区
    goodsCommentAdd, //添加商品评论
    goodsCommentList, //商品评论列表
    getCommentUserStatus, //商品评论列表用户相关状态
    goodsCommentThumbsUp, //商品评论点赞
    goodsCollectionList, //商品收藏列表
    deleteGoodsCollection, //删除商品收藏
    goodsCouponInfo, //商品优惠券信息
    packageDetailSeckill, //秒杀
    // 购物车板块
    addShoppingCart, //加入购物车
    shoppingCartList, //购物车列表
    shoppingCartCount, //购物车数量
    shoppingCartMoneyCalclute, //购物车勾选金额计算
    changeShoppingCartNums, //购物车数量加减
    shoppingCatDelete, //删除购物车

    // 确认订单板块
    calculateOrderMoney, //订单金额计算接口
    createOrder, //创建订单
    orderRelaText, //订单相关文本
    orderInvoiceDetailList, //订单发票明细列表
    orderAddPurchase, //订单加购

    // 支付板块
    payMethod, //银联支付
    queryUmsPayOrderStatus, //查询银联支付订单状态

    // 我的订单列表板块
    myOrderList, //我的订单列表
    cancelDeleteOrder, //用户取消、删除订单接口
    updateOrderStatus, //修改订单状态
    orderReceipt, //确认收货
    urlShorten, //短连接

    // 订单详情板块
    orderDetail, //订单详情
    queryCommonOrderDetail, //公共获取订单详情接口

    // 物流板块
    logisticsDetails, //物流详情
    logisticsCompanyList, //物流公司

    // 酒会板块
    winePartyList, //酒会列表
    winePartyDetail, //酒会详情
    winePartyOroupInfo, //酒会拼团信息
    winePartyCityList, //酒会城市列表
    winePartyCalculateOrderMoney, //酒会计算订单金额
    winePartyCreateOrder, //酒会创建订单
    myWinePartyList, //我的酒会列表
    winePartyOrderDetail, //酒会订单详情
    winePartyWriteOffCode, //酒会核销码

    // 酒闻板块
    wineSmellList, //获取酒闻列表
    wineSmellDetail, //酒闻详情
    wineSmellDetailJson, //获取酒闻详情期数json文件
    wineSmellCollectionStatus, //酒闻获取收藏状态
    wineSmellCommentAdd, //添加酒闻详情评论
    wineSmellCommentList, //酒闻详情评论列表
    wineSmellThumbsUp, //资讯（酒闻）\评论点赞前端
    cancelEnjoyWineComment,
    wineSmellCollectinList, //酒闻收藏列表
    wineSmellCollection, //酒闻收藏（收藏/取消收藏）

    // 订单开票板块
    personalOrderInvoiceList, //个人中心订单开票列表
    winePartyOrderInvoiceList, //酒会订单可开票列表

    // 猜你喜欢，热门推荐
    recommendList, //猜你喜欢热门推荐列表

    // 广告列表
    advertisingList, // 广告列表

    // 全局搜索
    globalSearch, // 全局搜索
    hotSearch, // 热门搜索

    // 直播列表
    ossLiveBroadcastList, // oss直播列表（直播源为oss）

    // 申请售后
    applyAfterSale, //申请售后
    afterSaleRefundMoney, //售后可退金额

    // 售后详情
    afterSaleDetail, //售后详情信息
    returnAndexchangeFillInfo, //退换货填写退货信息
    getPickUpDateList,

    // oss上传板块
    ossUpload, //oss上传

    // 支付抽奖
    payLuckyDraw, //支付抽奖大转盘
    payLuckyDrawList, //支付抽奖列表
    payLuckyDrawCount, //支付抽奖次数

    // 认证板块
    certificationInfo, //认证信息
    certificationList, //认证列表
    submitCertification, //提交认证申请

    // 兔头商城
    rabbitCombine, //兔头商城首页聚合接口
    rabbitCouponDetail, //兔头优惠券详情
    rabbitExchangeRecord, //兔头兑换记录
    rabbitExchangeCoupon, //兔头兑换优惠券
    rabbitGoodsDetail, //兔头商品详情
    rabbitCreateOrder, //兔头商品创建订单

    // 大转盘
    largeTurntableLuckyDrawList, //大转盘查询抽奖列表
    largeTurntableInfo, //大转盘查询抽奖信息
    largeTurntableLuckyDraw, //大转盘抽奖
    largeTurntablePriceRecord, //大转盘抽象记录
    newRabbitLuckDraw, //新人大转盘抽奖
    getNewRabbitRotaryDraw, //新人抽奖配置列表
    newRabbitShareDrawList, //新人兔头幸运转盘抽奖记录
    getNewcomerLotteryInfo, //新人兔头幸运转盘查询用户抽奖信息
    newcomerShare, //新人分享增加抽奖次数

    // 等级
    userLevelInfo, //用户等级信息

    // 更多酒款列表（卡片点击更多跳转的列表）
    moreThanWineList, //更多酒款列表

    // 门店
    storeAddRegister, //门店注册用户
    storeList, //门店列表
    storeInfo, //门店信息
    storeFilterList, //门店筛选列表
    storeGoodsList, //门店商品列表
    storeGoodsDetail, //门店商品详情
    storeGoodsAddShoppingCart, //门店商品加入购物车
    storeShoppingCartList, //门店购物车列表
    storeShoppingCartChangeNum, //门店购物车数量加减
    storeShoppingCartDel, //门店购物车删除
    storeOrderPrice, //门店订单金额
    storeCreateOrder, //门店创建订单
    storePay, //门店支付
    storeOrderList, //门店订单列表
    storeCanInvoiceList, //门店可开票订单列表
    storeOrderInvoiceList, //订单待开票列表
    storeOrderOpenInvoice, //门店订单开票接口
    storeOrderRefund, //门店订单申请退款
    storeOrderConfirmReceipt, //门店订单确认收货
    experienceCouponList, //体验券列表
    experienceCouponInfo, //体验券信息
    experienceCouponTake, //体验券领取
    storeDeliveryRange, //是否在门店配送范围内

    // 商家板块
    businessShipmentAddressList, //商家范围内发货地址列表
    businessOrderGoodsMatchInfo, //商家获取运费、3小时达数据（订单商品列表做匹配使用）
    businessGetGoodsListIdentification, //根据定位获取商家商品距离（商品列表获取标签时使用）
    businessCityList, //商家城市接口
    businessShipTips, //商家发货时间提示语

    // 酒评板块
    wineParams, //酒款参数
    getWineCommentList,
    createWineComment,
    getWineCommentDetail,
    getCommunityTopicList,
    getCommunityTopicDetail,

    // 工具类接口
    writeOffCode, //核销码
    regionId, //地区id
    channelKeyword, //频道关键字
    newPeopleMarketingAd, //新人营销广告
    registerUidByTencentMoments, //将用户设置为腾讯朋友圈广告过来的标识
    newPeopleCouponPackage, //优惠券包发放

    winepartyActivityDetail,
    winepartyActivityGoodsList,

    inviteActivityDetail,
    inviteActivityGoodsList,
    getInviteActivityTicket,

    getCouponDetail,
    getNewcomerGoodsList,

    reportBuryDot,

    // 拍卖板块
    getPersonAuctionOrderInfo,
    searchAuctionMyBuyingList,
    findProductListByPeriod,
    getPersonAuctionConfig,
    createPersonAuctionGoods,
    getPersonAuctionGoods,
    getPersonAuctionGoodsRelease,
    resubmitPersonAuctionGoods,
    preResubmitPersonAuctionGoods,
    getShipAddressRegionList,
    getFreightEstimate,
    searchAuctionMyCreateList,
    getPendDeliverCount,
    abandonPersonAuction,
    getAuctionPickUpList,
    deliverPersonGoods,
    getAuctionAdDetail,
    getAuctionIndexBannerList,
    getAuctionIndexAreaList,
    searchAuctionIndexGoodsList,
    auctionIndexGoodsListFirstBanner,
    searchAuctionActivityGoodsList,
    getAuctionGoodsDetail,
    getAuctionRecommendGoodsList,
    getAuctionHotRecommendGoodsList,
    createAuctionGoods,
    searchAuctionMyGoodsList,
    delAuctionGoods,
    searchAuctionCertificateList,
    updateAuctionCertificateShareStatus,
    addEnjoyAuctionGoods,
    cancelEnjoyAuctionGoods,
    batchCancelEnjoyAuctionGoods,
    searchEnjoyAuctionGoodsList,
    searchRemindAuctionGoodsList,
    editRemindAuctionGoods,
    getAuctionBidRecords,
    auctionBid,
    auctionBuyerOrderList, //我拍到的订单列表
    auctionBuyerAfterSaleList, //买家售后列表
    auctionSellerOrderList, //我卖出的订单列表
    auctionSellerAfterSaleList, //卖家售后列表
    auctionRemindNotice, //拍卖提醒通知
    auctionConfirmReceipt, //拍卖确认收货
    auctionOrderDetail, //买家/卖家订单详情
    auctionOrderEvaluate, //拍卖订单评价
    auctionOrderEvaluateList, //评价列表
    auctionOrderEvaluateListLike, //评价列表点赞
    auctionOrderEvaluateListDislike, //评价列表取消点赞
    auctionParticipateList, //我的参拍列表
    auctionParticipateListDel, //删除我的参拍
    auctionAfterSaleApply, //拍卖申请售后
    auctionAfterSaleDetail, //拍卖售后详情
    auctionAfterSaleDetailAudit, //同意/拒绝售后申请（待处理）
    auctionAfterSaleDetailSellerReceiptDeal, //卖家同意/拒绝收货（待收货）
    auctionPickupPeriod, //拍卖取件时间段
    auctionSubmitPickupInfo, //提交上门取件信息
    auctionAfterSalesSpeed, //售后进度
    getAuctionGoodsComments,
    addAuctionGoodsComment,
    addEnjoyAuctionGoodsComment,
    cancelEnjoyAuctionGoodsComment,
    createAuctionEarnestOrder,
    getAuctionEarnestOrderDetail,
    getAuctionEarnestSimpleOrderDetail,
    getAuctionEarnestStatus,
    searchAuctionFundsList,
    searchAuctionFundsListNew,
    getAuctionFundsDetail,
    getAuctionFundsStats,
    searchAuctionEarnestList,
    addAuctionRNInfo,
    editAuctionRNInfo,
    getAuctionRNInfo,
    getAuctionUserInfo,
    getAuctionMineStats,
    getAuctionMsg,
    getAuctionMsgDetail,
    searchAuctionCreditValuesList,
    addAuctionBank,
    removeAuctionBank,
    searchAuctionBankList,
    createAuctionPayeeAccount,
    updateAuctionPayeeAccount,
    searchAuctionPayeeAccounts,
    searchGrapeVarietyList,
    searchWineGradeOrgList,
    getJsapiSign,
    appPayment,
    getWxPayOpenIdByCode,
    getWxOABindStatus,
    bindWxOA,
    getPayToPublicKefu,
    // 再来一单板块
    oneMoreOrder, //再来一单
    iWant, //我想要
    addWishList, //加入心愿清单
    wishList, //心愿清单列表
    wishListRemove, //心愿清单列表移除
    queryActivityGoodsList,
    getActivityByPeriods,
    getWineCommentRHCount,
    getSecondCombine,
    getRandomProductCategory,
    getSecondRecommendList,
    filterSecondDataList,
    getSecondGoodsRecommendList,
    reportUserPortrait,
    reportUserPortraitLabel,
    secondSearch,
    getSecondRecommendIncrement,
    getSearchFeedbackCount,
    addSeachFeedback,

    // 新人板块
    newPeopleCouponActivityInfo, //领券信息+新人列表
    newPeopleReceiveBenefits, //领券

    // 订金板块
    depositOrderList, //订金订单列表
    depositOrderDetail, //订金订单详情

    // 首三单板块
    topThreeOrderNums, //获取用户首三单订单个数

    // 启动页切换板块
    userChangePageConfig, //切换页面配置
    addInvestigatesFeedback,
    getInventoryRemainInfo,
    updateOrderAddressValidation,
    updateOrderAddress,
    getAuctionEarnestCouponList,
    recordPreSalesSource,
    updateOrderTsStatus,
    upgradeColdChain,
    cancelAfterSale,
    updateOrderInvoiceInfo,
    getUserReturningInfo,
    storeApplyInvoice,
    getStoreApplyInvoiceStatus,
    getGoodsFullReductionLabel,
    reportWasmSupport,

    //学堂
    studyTopicList,
    studyBannerList,
    studyRankingList,

    // 社区相关接口注册
    getTopicList,
    getTopicDetail,
    postsLike,
    focusUser,
    topicForList,
    wineEvaluationList,
    wineEvaluationLike,
    createPost,
    postsDetail,
    postsCommentList,
    commentList,
    postMakeComment,
    wineMakeComment,
    getOtherData,
    collection,
    getUserInfo,
    postsList,
    selfPostsList,
    myWineEvaluationList,
    getVOSUid,

    //充值卡
    myBalanceHistory,
    myGiftCardsList,
    giftCardList,
    myCurrentBalance,
    giftCardOrderCreate,
    giftCarduseVirtual,
    giftCardSend,
    giftCardsOrderDetail,
    balancePay,
    giftCardsDetail,
    giftCardsInfo
  }
}

export default { install }
